# CodeAgent Setup and Verification Guide

## How to Run and Verify the CodeAgent with Model

This guide shows you how to set up, run, and verify the CodeAgent functionality with different models.

---

## 🚀 Quick Start

### 1. **Set Up Hugging Face Token**

First, you need a Hugging Face API token:

```bash
# Get your token from https://huggingface.co/settings/tokens
export HF_TOKEN="hf_your_token_here"

# Verify it's set
echo $HF_TOKEN
```

### 2. **Basic CodeAgent Test**

```bash
# Test with CodeAgent (requires HF_TOKEN)
python main.py test_java_files/Test1.java

# Test fallback mode (no token needed)
python main.py --fallback test_java_files/Test1.java
```

---

## 📋 Step-by-Step Verification

### Step 1: Verify Dependencies

```bash
cd /home/<USER>/project/CodexJava

# Check if smolagents is installed
python3 -c "import smolagents; print('✅ smolagents available')"

# Check if our tools work
python3 -c "from java_parser_tool import parse_java_file; print('✅ Tools available')"
```

### Step 2: Test Model Initialization

```bash
# Test model creation
python3 -c "
from smolagents import HfApiModel
model = HfApiModel('mistralai/Mistral-7B-Instruct-v0.2')
print('✅ Model initialized:', type(model))
"
```

### Step 3: Test CodeAgent Creation

```bash
# Test agent creation
python3 -c "
from smolagents import CodeAgent, HfApiModel
from java_parser_tool import parse_java_file

model = HfApiModel('mistralai/Mistral-7B-Instruct-v0.2')
agent = CodeAgent(model=model, tools=[parse_java_file])
print('✅ CodeAgent created:', type(agent))
"
```

### Step 4: Test Full Execution

```bash
# Run with verbose output to see what's happening
python main.py --verbose test_java_files/Test1.java
```

---

## 🔧 Different Ways to Run

### Method 1: Using main.py (Recommended)

```bash
# With CodeAgent (requires HF_TOKEN)
python main.py test_java_files/Test1.java

# With custom model
python main.py --model "microsoft/DialoGPT-medium" test_java_files/Test1.java

# Fallback mode (no AI, uses simple_parser directly)
python main.py --fallback test_java_files/Test1.java

# Verbose mode for debugging
python main.py --verbose --fallback test_java_files/Test1.java

# Custom output file
python main.py --output my_analysis.json test_java_files/Test1.java
```

### Method 2: Direct Tool Usage

```bash
# Use the tool directly (no AI agent)
python java_parser_tool.py test_java_files/Test1.java

# Use simple parser directly
python simple_parser.py test_java_files/Test1.java
```

### Method 3: Interactive Python

```python
# In Python shell
from smolagents import CodeAgent, HfApiModel
from java_parser_tool import parse_java_file

# Initialize
model = HfApiModel("mistralai/Mistral-7B-Instruct-v0.2")
agent = CodeAgent(model=model, tools=[parse_java_file])

# Run
result = agent.run("Execute parse_java_file with file_path 'test_java_files/Test1.java'")
print(result)
```

---

## 🎯 Supported Models

The system supports various Hugging Face models:

### Recommended Models

```bash
# Mistral (default, good performance)
python main.py --model "mistralai/Mistral-7B-Instruct-v0.2" test_java_files/Test1.java

# Zephyr (alternative)
python main.py --model "HuggingFaceH4/zephyr-7b-beta" test_java_files/Test1.java

# DialoGPT (smaller, faster)
python main.py --model "microsoft/DialoGPT-medium" test_java_files/Test1.java
```

### Model Configuration

You can also modify the model in the code:

```python
# In main.py, change this line:
model = HfApiModel("your-preferred-model-here")
```

---

## 🔍 Verification Commands

### Complete Verification Suite

```bash
# Run all test files
for file in test_java_files/*.java; do
    echo "Testing $file"
    python main.py --fallback "$file"
    echo "---"
done
```

### Check Output Quality

```bash
# Verify JSON output structure
python3 -c "
import json
with open('output/Test1_summary.json') as f:
    data = json.load(f)
print('File:', data['file_path'])
print('Classes:', len(data['classes']))
print('Errors:', len(data['errors']))
for cls in data['classes']:
    print(f'  - {cls[\"class_name\"]}: {len(cls[\"methods\"])} methods')
"
```

### Performance Test

```bash
# Time the execution
time python main.py --fallback test_java_files/Test1.java
```

---

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. **HF_TOKEN Not Set**
```
Error: HTTP 401 Unauthorized
```
**Solution:**
```bash
export HF_TOKEN="your_token_here"
# Or use fallback mode:
python main.py --fallback test_java_files/Test1.java
```

#### 2. **Model Loading Issues**
```
Error: Model not found or access denied
```
**Solution:**
```bash
# Try a different model
python main.py --model "microsoft/DialoGPT-medium" test_java_files/Test1.java

# Or use fallback
python main.py --fallback test_java_files/Test1.java
```

#### 3. **Network Issues**
```
Error: Connection timeout
```
**Solution:**
```bash
# Use fallback mode (works offline)
python main.py --fallback test_java_files/Test1.java
```

#### 4. **Import Errors**
```
ModuleNotFoundError: No module named 'smolagents'
```
**Solution:**
```bash
pip install smolagents huggingface-hub
```

---

## 📊 Expected Output

### Successful CodeAgent Run

```bash
$ python main.py test_java_files/Test1.java

Analysis complete. Results saved to: output/Test1_summary.json

==================================================
ANALYSIS SUMMARY
==================================================
File: test_java_files/Test1.java
Errors: None
Classes found: 1
  - Test1: 5 methods
    Methods: methodA, methodB, privateMethod
    ... and 2 more
```

### JSON Output Format

```json
{
    "file_path": "test_java_files/Test1.java",
    "classes": [
        {
            "class_name": "Test1",
            "type": "class",
            "methods": [
                "methodA",
                "methodB", 
                "privateMethod",
                "staticMethod",
                "Test1"
            ]
        }
    ],
    "package": "com.example",
    "imports": [],
    "errors": []
}
```

---

## 🎯 Testing Different Scenarios

### Test All File Types

```bash
# Basic class
python main.py test_java_files/Test1.java

# Multiple classes
python main.py test_java_files/Test2.java

# Interface
python main.py test_java_files/Interface.java

# Empty class
python main.py test_java_files/Empty.java

# Syntax errors
python main.py test_java_files/Broken.java
```

### Compare Agent vs Fallback

```bash
# With CodeAgent
python main.py test_java_files/Test1.java

# With fallback
python main.py --fallback test_java_files/Test1.java

# Compare outputs
diff output/Test1_summary.json output/Test1_summary.json
```

---

## ✅ Success Indicators

You know the CodeAgent is working when:

1. ✅ **No import errors** when running main.py
2. ✅ **JSON files generated** in output/ directory
3. ✅ **Correct class/method extraction** in JSON
4. ✅ **No errors** in the errors array (for valid Java files)
5. ✅ **Consistent results** between agent and fallback modes

---

## 🚀 Next Steps

Once CodeAgent is verified:

1. **Experiment with different models** to find the best performance
2. **Test with your own Java files** beyond the test cases
3. **Integrate with your development workflow**
4. **Prepare for Phase 1** implementation (request flow analysis)

---

## 📞 Support

If you encounter issues:

1. **Check the logs** with `--verbose` flag
2. **Use fallback mode** for guaranteed functionality
3. **Verify dependencies** are installed correctly
4. **Check HF_TOKEN** is set and valid

The system is designed to be robust - if CodeAgent fails, fallback mode ensures you can always parse Java files!
