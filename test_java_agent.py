#!/usr/bin/env python3
"""
Test Java parsing using the exact same pattern as DuckDuckGo example
"""

from smolagents import CodeAgent, HfApiModel
from java_parser_tool import parse_java_file

# Create agent using exact same pattern as DuckDuckGo example
agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())

# Run agent with Java file analysis
response = agent.run("Use parse_java_file to analyze the file 'test_java_files/Test1.java' and return the JSON result")

print("Agent Response:")
print(response)
