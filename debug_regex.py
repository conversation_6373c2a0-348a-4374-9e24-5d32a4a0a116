#!/usr/bin/env python3
"""Debug script to test regex patterns"""

import re

def test_class_regex():
    content = """package com.example;

/**
 * Test class for basic Java parsing functionality
 */
public class Test1 {
    private int fieldX;
    private String name;
    
    public Test1() {
        this.fieldX = 0;
        this.name = "default";
    }
    
    public void methodA() {
        System.out.println("Method A called");
    }
    
    public int methodB(String input) {
        return input.length();
    }
    
    private void privateMethod() {
        // Private method implementation
    }
    
    public static void staticMethod() {
        System.out.println("Static method called");
    }
}"""

    print("Testing class regex patterns...")
    
    # Test different patterns
    patterns = [
        r'^\s*(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?(?:abstract\s+)?class\s+(\w+)',
        r'(?:^|\n)\s*(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?(?:abstract\s+)?class\s+(\w+)',
        r'\bclass\s+(\w+)',
    ]
    
    for i, pattern_str in enumerate(patterns):
        print(f"\nPattern {i+1}: {pattern_str}")
        pattern = re.compile(pattern_str, re.MULTILINE)
        matches = pattern.findall(content)
        print(f"Matches: {matches}")

if __name__ == "__main__":
    test_class_regex()
