#!/usr/bin/env python3
"""
Clean version of main.py that prioritizes reliability
Uses fallback mode by default with option to try AI
"""

import sys
import json
import os
import argparse
from typing import Dict, Any

# Try to import smolagents components
try:
    from smolagents import CodeAgent
    try:
        from smolagents import InferenceClientModel as ModelClass
    except ImportError:
        from smolagents import HfApiModel as ModelClass
    SMOLAGENTS_AVAILABLE = True
except ImportError:
    SMOLAGENTS_AVAILABLE = False

# Import our Java parser tool
from java_parser_tool import parse_java_file, parse_java_file_simple


def setup_argument_parser():
    """Set up command-line argument parsing."""
    parser = argparse.ArgumentParser(
        description="AI-Powered Java Coding Assistant - Clean Version",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_clean.py test_java_files/Test1.java
  python main_clean.py --try-ai test_java_files/Test1.java
  python main_clean.py --output custom.json test_java_files/Test1.java
        """
    )
    
    parser.add_argument(
        "java_file",
        help="Path to the Java file to analyze"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file path (default: output/<filename>_summary.json)"
    )
    
    parser.add_argument(
        "--try-ai",
        action="store_true",
        help="Try AI agent first (may show errors but falls back gracefully)"
    )
    
    parser.add_argument(
        "--model",
        default="mistralai/Mistral-7B-Instruct-v0.2",
        help="Hugging Face model to use (default: mistralai/Mistral-7B-Instruct-v0.2)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    return parser


def create_output_filename(java_file_path: str, custom_output: str = None) -> str:
    """Create an appropriate output filename."""
    if custom_output:
        return custom_output
        
    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename based on input file
    base_filename = os.path.basename(java_file_path)
    name_without_ext = os.path.splitext(base_filename)[0]
    return os.path.join(output_dir, f"{name_without_ext}_summary.json")


def run_reliable_mode(java_file_path: str, verbose: bool = False) -> Dict[str, Any]:
    """Run in reliable mode using direct tool call."""
    if verbose:
        print(f"Running in reliable mode for {java_file_path}")
    
    return parse_java_file(java_file_path)


def run_with_ai_attempt(java_file_path: str, model_name: str, verbose: bool = False) -> Dict[str, Any]:
    """Attempt to run with AI, fall back gracefully if needed."""
    if not SMOLAGENTS_AVAILABLE:
        if verbose:
            print("smolagents not available, using reliable mode")
        return run_reliable_mode(java_file_path, verbose)
    
    # Check for HF_TOKEN
    hf_token = os.getenv('HF_TOKEN')
    if not hf_token and verbose:
        print("Warning: HF_TOKEN not set. AI may fail and fall back to reliable mode.")
    
    try:
        if verbose:
            print(f"Attempting AI analysis with model: {model_name}")
        
        # Initialize components
        model = ModelClass(model_name)
        agent = CodeAgent(model=model, tools=[parse_java_file])
        
        # Simple, direct prompt
        prompt = f"Execute parse_java_file with file_path='{java_file_path}'"
        
        if verbose:
            print("Calling AI agent...")
        
        # Try agent execution
        response = agent.run(prompt)
        
        if verbose:
            print(f"AI response received: {type(response)}")
        
        # Handle response
        if response is None:
            if verbose:
                print("AI returned None, using reliable mode")
            return run_reliable_mode(java_file_path, verbose)
        
        # Try to parse response
        response_str = str(response)
        try:
            result = json.loads(response_str)
            if verbose:
                print("✅ AI analysis successful!")
            return result
        except json.JSONDecodeError:
            if verbose:
                print("AI response not JSON, using reliable mode")
            return run_reliable_mode(java_file_path, verbose)
            
    except Exception as e:
        if verbose:
            print(f"AI attempt failed ({e}), using reliable mode")
        return run_reliable_mode(java_file_path, verbose)


def save_output(data: Dict[str, Any], output_path: str, verbose: bool = False) -> None:
    """Save the analysis results to a JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        if verbose:
            print(f"Results saved to: {output_path}")
        else:
            print(f"Analysis complete. Results saved to: {output_path}")
            
    except Exception as e:
        print(f"Error saving output to {output_path}: {e}")


def print_summary(data: Dict[str, Any]) -> None:
    """Print a brief summary of the analysis results."""
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    
    print(f"File: {data.get('file_path', 'Unknown')}")
    
    if data.get('errors'):
        print(f"Errors: {len(data['errors'])}")
        for error in data['errors']:
            print(f"  - {error}")
    else:
        print("Errors: None")
    
    classes = data.get('classes', [])
    print(f"Classes found: {len(classes)}")
    
    for class_info in classes:
        class_name = class_info.get('class_name', 'Unknown')
        methods = class_info.get('methods', [])
        if isinstance(methods, list) and methods:
            method_count = len(methods)
            method_names = methods[:3] if isinstance(methods[0], str) else [m.get('name', 'Unknown') for m in methods[:3]]
            
            print(f"  - {class_name}: {method_count} methods")
            if method_names:
                print(f"    Methods: {', '.join(method_names)}")
                if method_count > 3:
                    print(f"    ... and {method_count - 3} more")
        else:
            print(f"  - {class_name}: No methods")


def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.java_file):
        print(f"Error: File not found: {args.java_file}")
        sys.exit(1)
    
    if not args.java_file.endswith('.java'):
        print(f"Warning: File does not have .java extension: {args.java_file}")
    
    # Determine output file path
    output_path = create_output_filename(args.java_file, args.output)
    
    try:
        # Run analysis
        if args.try_ai:
            output_data = run_with_ai_attempt(args.java_file, args.model, args.verbose)
        else:
            output_data = run_reliable_mode(args.java_file, args.verbose)
        
        # Save results
        save_output(output_data, output_path, args.verbose)
        
        # Print summary
        print_summary(output_data)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
