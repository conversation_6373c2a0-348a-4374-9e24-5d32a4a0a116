# AI-Powered Java Coding Assistant - Phase 0

An AI-powered command-line tool for analyzing Java source code and generating structured documentation. This Phase 0 implementation focuses on basic Java file parsing using `javalang` and `smolagents` for AI orchestration.

## Features

- **Java Code Parsing**: Parse Java source files and extract class/method information
- **AI Integration**: Uses `smolagents` with configurable LLM models for intelligent processing
- **Structured Output**: Generates JSON summaries suitable for RAG (Retrieval-Augmented Generation) ingestion
- **Error Handling**: Graceful handling of syntax errors and file issues
- **Fallback Mode**: Works without AI agent when needed

## Requirements

- Python 3.8+
- Java source files to analyze
- (Optional) Hugging Face API token for AI features

## Installation

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd CodexJava
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Hugging Face API token** (optional, for AI features):
   ```bash
   export HF_TOKEN="your_hf_api_token_here"
   ```

## Usage

### Basic Usage

Analyze a Java file and generate a JSON summary:

```bash
python main.py test_java_files/Test1.java
```

### Advanced Options

```bash
# Specify custom output file
python main.py --output my_analysis.json test_java_files/Test1.java

# Use fallback mode (no AI agent)
python main.py --fallback test_java_files/Test1.java

# Use a different AI model
python main.py --model "microsoft/DialoGPT-medium" test_java_files/Test1.java

# Enable verbose output
python main.py --verbose test_java_files/Test1.java
```

### Command-Line Options

- `java_file`: Path to the Java file to analyze (required)
- `--output, -o`: Custom output file path
- `--fallback`: Use fallback mode without smolagents
- `--model`: Hugging Face model to use (default: mistralai/Mistral-7B-Instruct-v0.2)
- `--verbose, -v`: Enable verbose output
- `--help`: Show help message

## Output Format

The tool generates JSON files with the following structure:

```json
{
  "file_path": "test_java_files/Test1.java",
  "classes": [
    {
      "class_name": "Test1",
      "methods": [
        {
          "name": "methodA",
          "modifiers": ["public"],
          "return_type": "void",
          "parameters": []
        },
        {
          "name": "methodB",
          "modifiers": ["public"],
          "return_type": "int",
          "parameters": [
            {
              "name": "input",
              "type": "String"
            }
          ]
        }
      ],
      "fields": [
        {
          "name": "fieldX",
          "type": "int",
          "modifiers": ["private"]
        }
      ],
      "modifiers": ["public"],
      "extends": null,
      "implements": []
    }
  ],
  "package": "com.example",
  "imports": [],
  "errors": []
}
```

## Testing

The project includes several test Java files in the `test_java_files/` directory:

- `Test1.java`: Basic class with methods and fields
- `Test2.java`: Multiple classes in one file
- `Empty.java`: Empty class for edge case testing
- `Broken.java`: Syntactically incorrect Java for error handling testing
- `Interface.java`: Interface declaration testing

Run tests with:

```bash
python main.py test_java_files/Test1.java
python main.py test_java_files/Test2.java
python main.py test_java_files/Empty.java
python main.py test_java_files/Broken.java
python main.py test_java_files/Interface.java
```

## Project Structure

```
CodexJava/
├── main.py                 # Main CLI application
├── java_parser_tool.py     # Java parsing logic with smolagents integration
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── .gitignore             # Git ignore rules
├── docs/                  # Project documentation
│   ├── 0.ProductRequirementsDocument.md
│   └── 1.Phase0TaskDetail.md
├── test_java_files/       # Test Java files
│   ├── Test1.java
│   ├── Test2.java
│   ├── Empty.java
│   ├── Broken.java
│   └── Interface.java
└── output/                # Generated JSON outputs (created automatically)
```

## Dependencies

- `smolagents`: AI agent framework for LLM integration
- `javalang`: Java source code parsing library
- `huggingface-hub`: Hugging Face API integration
- `requests`: HTTP library for API calls

## Error Handling

The tool handles various error conditions gracefully:

- **File not found**: Reports missing files in the errors array
- **Java syntax errors**: Captures parsing errors with location information
- **AI agent failures**: Falls back to direct parsing when AI integration fails
- **Network issues**: Handles API connectivity problems

## Future Phases

This Phase 0 implementation provides the foundation for future enhancements:

- **Phase 1**: Request flow analysis from servlet entry points to SQL queries
- **Phase 2**: RAG implementation with vector database integration
- **Phase 3**: Mermaid diagram generation and advanced documentation

## Contributing

This project follows the phased development approach outlined in the Product Requirements Document. Please refer to the documentation in the `docs/` directory for detailed requirements and specifications.

## License

[Add your license information here]
