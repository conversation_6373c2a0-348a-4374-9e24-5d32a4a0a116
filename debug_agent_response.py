#!/usr/bin/env python3
"""
Debug script to understand what's happening with agent.run() response
"""

import os
import sys
from java_parser_tool import parse_java_file

def debug_agent_response():
    """Debug the agent response issue step by step"""
    print("="*60)
    print("DEBUGGING AGENT RESPONSE ISSUE")
    print("="*60)
    
    try:
        from smolagents import CodeAgent
        try:
            from smolagents import InferenceClientModel as ModelClass
        except ImportError:
            from smolagents import HfApiModel as ModelClass
        
        print("✅ Imports successful")
        
        # Initialize model
        model_name = "mistralai/Mistral-7B-Instruct-v0.2"
        print(f"Initializing model: {model_name}")
        model = ModelClass(model_name)
        print("✅ Model initialized")
        
        # Create agent
        print("Creating CodeAgent...")
        agent = CodeAgent(model=model, tools=[parse_java_file])
        print("✅ CodeAgent created")
        
        # Test the exact prompt from main.py
        java_file_path = "test_java_files/Test1.java"
        prompt = f"""
        Please analyze the Java file located at '{java_file_path}'.
        Use the 'parse_java_file' tool to extract information about its classes and methods.
        Return only the structured JSON output from the tool, without any additional commentary.
        """
        
        print(f"Testing with file: {java_file_path}")
        print(f"Prompt length: {len(prompt)} characters")
        print("Prompt preview:", prompt[:100] + "...")
        
        print("\n" + "-"*40)
        print("CALLING agent.run()...")
        print("-"*40)
        
        try:
            response = agent.run(prompt)
            
            print("✅ agent.run() completed")
            print(f"Response type: {type(response)}")
            print(f"Response is None: {response is None}")
            
            if response is not None:
                response_str = str(response)
                print(f"Response length: {len(response_str)}")
                print(f"Response preview: {response_str[:200]}...")
                
                # Check if it contains JSON
                if '{' in response_str and '}' in response_str:
                    print("✅ Response contains JSON-like content")
                else:
                    print("⚠️  Response doesn't look like JSON")
                
                # Try to parse as JSON
                import json
                try:
                    json_data = json.loads(response_str)
                    print("✅ Response is valid JSON")
                    print(f"JSON keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'Not a dict'}")
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing failed: {e}")
                    
                    # Try to extract JSON
                    import re
                    json_match = re.search(r'\{.*\}', response_str, re.DOTALL)
                    if json_match:
                        try:
                            extracted_json = json.loads(json_match.group())
                            print("✅ Extracted JSON successfully")
                            print(f"Extracted keys: {list(extracted_json.keys())}")
                        except json.JSONDecodeError:
                            print("❌ Even extracted content is not valid JSON")
                    else:
                        print("❌ No JSON pattern found in response")
            else:
                print("❌ Response is None")
                
        except Exception as agent_error:
            print(f"❌ agent.run() failed: {agent_error}")
            print(f"Error type: {type(agent_error)}")
            return False
            
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False
    
    return True

def test_direct_tool_comparison():
    """Compare with direct tool call"""
    print("\n" + "="*60)
    print("COMPARING WITH DIRECT TOOL CALL")
    print("="*60)
    
    try:
        result = parse_java_file("test_java_files/Test1.java")
        print("✅ Direct tool call successful")
        print(f"Result type: {type(result)}")
        print(f"Result keys: {list(result.keys())}")
        print(f"Classes found: {len(result.get('classes', []))}")
        
        import json
        json_str = json.dumps(result, indent=2)
        print(f"JSON length: {len(json_str)}")
        print("JSON preview:")
        print(json_str[:300] + "...")
        
        return result
        
    except Exception as e:
        print(f"❌ Direct tool call failed: {e}")
        return None

def suggest_fixes():
    """Suggest fixes for the agent response issue"""
    print("\n" + "="*60)
    print("SUGGESTED FIXES")
    print("="*60)
    
    print("🔧 Fix 1: Improve prompt to force tool usage")
    print("   Change prompt to explicitly request tool execution")
    
    print("\n🔧 Fix 2: Add response validation")
    print("   Check response type and content before processing")
    
    print("\n🔧 Fix 3: Enhance fallback mechanism")
    print("   Always fall back to direct tool call if agent fails")
    
    print("\n🔧 Fix 4: Use different model")
    print("   Try a model that's more compatible with tool calling")
    
    print("\n🔧 Fix 5: Simplify the approach")
    print("   Use fallback mode by default for reliability")

def main():
    """Run all debugging steps"""
    print("DEBUGGING: Why agent.run() doesn't return usable response")
    
    # Test agent response
    agent_works = debug_agent_response()
    
    # Test direct tool
    direct_result = test_direct_tool_comparison()
    
    # Suggest fixes
    suggest_fixes()
    
    # Summary
    print("\n" + "="*60)
    print("DEBUGGING SUMMARY")
    print("="*60)
    
    if agent_works:
        print("✅ Agent setup works - issue is in response handling")
    else:
        print("❌ Agent setup has issues")
    
    if direct_result:
        print("✅ Direct tool call works perfectly")
    else:
        print("❌ Direct tool call has issues")
    
    print("\n🎯 RECOMMENDATION:")
    if direct_result:
        print("Use fallback mode for reliable results:")
        print("python main.py --fallback test_java_files/Test1.java")
    
    print("\n🔍 NEXT STEPS:")
    print("1. Check the actual agent response content")
    print("2. Improve response parsing logic")
    print("3. Consider using fallback as default")

if __name__ == "__main__":
    main()
