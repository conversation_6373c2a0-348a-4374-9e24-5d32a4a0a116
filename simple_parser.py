#!/usr/bin/env python3
"""
Simple Java Parser - Fallback implementation without external dependencies
This provides basic Java parsing functionality using only Python standard library.
"""

import re
import json
import os
import sys
from typing import Dict, List, Any


class SimpleJavaParser:
    """A simple Java parser using regular expressions."""

    def __init__(self):
        # Regular expressions for parsing Java constructs
        self.class_pattern = re.compile(r'^\s*(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?(?:abstract\s+)?class\s+(\w+)', re.MULTILINE)
        self.interface_pattern = re.compile(r'^\s*(?:public\s+|private\s+|protected\s+)?interface\s+(\w+)', re.MULTILINE)
        self.method_pattern = re.compile(r'^\s*(?:public\s+|private\s+|protected\s+|static\s+|final\s+|abstract\s+)*\s*(?:\w+(?:<[^>]*>)?\s+)*(\w+)\s*\([^)]*\)\s*(?:throws\s+[\w\s,]+)?\s*[{;]', re.MULTILINE)
        self.package_pattern = re.compile(r'package\s+([\w.]+)\s*;')
        self.import_pattern = re.compile(r'import\s+(?:static\s+)?([\w.*]+)\s*;')

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """Parse a Java file and extract basic information."""
        result = {
            "file_path": file_path,
            "classes": [],
            "package": None,
            "imports": [],
            "errors": []
        }

        try:
            if not os.path.exists(file_path):
                result["errors"].append(f"File not found: {file_path}")
                return result

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract package
            package_match = self.package_pattern.search(content)
            if package_match:
                result["package"] = package_match.group(1)

            # Extract imports
            import_matches = self.import_pattern.findall(content)
            result["imports"] = import_matches

            # Extract classes
            class_matches = self.class_pattern.findall(content)
            for class_name in class_matches:
                class_info = {
                    "class_name": class_name,
                    "type": "class",
                    "methods": self._extract_methods_for_class(content, class_name)
                }
                result["classes"].append(class_info)

            # Extract interfaces
            interface_matches = self.interface_pattern.findall(content)
            for interface_name in interface_matches:
                interface_info = {
                    "class_name": interface_name,
                    "type": "interface",
                    "methods": self._extract_methods_for_class(content, interface_name)
                }
                result["classes"].append(interface_info)

        except Exception as e:
            result["errors"].append(f"Error parsing {file_path}: {str(e)}")

        return result

    def _extract_methods_for_class(self, content: str, class_name: str) -> List[str]:
        """Extract method names for a specific class."""
        methods = []

        # Find the class definition (must be at start of line, not in comments)
        class_start_pattern = re.compile(rf'^\s*(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?(?:abstract\s+)?class\s+{re.escape(class_name)}\s*(?:extends\s+\w+\s*)?(?:implements\s+[\w\s,]+\s*)?\{{', re.MULTILINE)
        class_match = class_start_pattern.search(content)

        if not class_match:
            # Try interface pattern
            interface_start_pattern = re.compile(rf'^\s*(?:public\s+|private\s+|protected\s+)?interface\s+{re.escape(class_name)}\s*(?:extends\s+[\w\s,]+\s*)?\{{', re.MULTILINE)
            class_match = interface_start_pattern.search(content)

        if class_match:
            # Find the class body (simplified - doesn't handle nested classes perfectly)
            start_pos = class_match.end()
            brace_count = 1
            pos = start_pos

            while pos < len(content) and brace_count > 0:
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                pos += 1

            if brace_count == 0:
                class_body = content[start_pos:pos-1]

                # Extract methods from class body
                method_matches = self.method_pattern.findall(class_body)
                java_keywords = {'if', 'for', 'while', 'switch', 'try', 'catch', 'finally', 'do', 'else', 'return', 'break', 'continue', 'new', 'this', 'super', 'null', 'true', 'false'}
                for method_name in method_matches:
                    # Filter out constructor and common non-method patterns
                    if (method_name != class_name and  # Not a constructor
                        method_name.lower() not in java_keywords and  # Not keywords
                        method_name.isalpha() and  # Valid identifier
                        not method_name.islower() or len(method_name) > 3):  # Avoid single letters and short words
                        methods.append(method_name)

        return list(set(methods))  # Remove duplicates


def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python simple_parser.py <java_file_path> [output_file]")
        print("Example: python simple_parser.py test_java_files/Test1.java")
        sys.exit(1)

    java_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    # Parse the Java file
    parser = SimpleJavaParser()
    result = parser.parse_file(java_file)

    # Generate output filename if not provided
    if not output_file:
        os.makedirs("output", exist_ok=True)
        base_name = os.path.splitext(os.path.basename(java_file))[0]
        output_file = f"output/{base_name}_simple_summary.json"

    # Save results
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"Analysis complete. Results saved to: {output_file}")
    except Exception as e:
        print(f"Error saving results: {e}")
        sys.exit(1)

    # Print summary
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    print(f"File: {result['file_path']}")
    print(f"Package: {result.get('package', 'None')}")
    print(f"Imports: {len(result.get('imports', []))}")
    print(f"Classes/Interfaces: {len(result.get('classes', []))}")

    if result.get('errors'):
        print(f"Errors: {len(result['errors'])}")
        for error in result['errors']:
            print(f"  - {error}")

    for class_info in result.get('classes', []):
        class_name = class_info.get('class_name', 'Unknown')
        class_type = class_info.get('type', 'class')
        methods = class_info.get('methods', [])
        print(f"  - {class_name} ({class_type}): {len(methods)} methods")
        if methods:
            print(f"    Methods: {', '.join(methods[:5])}")
            if len(methods) > 5:
                print(f"    ... and {len(methods) - 5} more")


if __name__ == "__main__":
    main()
