/**
 * Test file with multiple classes in the same file
 */
public class Test2 {
    public void methodX() {
        System.out.println("Method X");
    }
    
    public void methodY(String s) {
        System.out.println("Method Y: " + s);
    }
    
    public int calculate(int a, int b) {
        return a + b;
    }
}

class AnotherClassInSameFile {
    void packageMethod() {
        System.out.println("Package method");
    }
    
    protected void protectedMethod() {
        // Protected method
    }
    
    public String getName() {
        return "AnotherClass";
    }
}
