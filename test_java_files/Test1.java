package com.example;

/**
 * Test class for basic Java parsing functionality
 */
public class Test1 {
    private int fieldX;
    private String name;
    
    public Test1() {
        this.fieldX = 0;
        this.name = "default";
    }
    
    public void methodA() {
        System.out.println("Method A called");
    }
    
    public int methodB(String input) {
        return input.length();
    }
    
    private void privateMethod() {
        // Private method implementation
    }
    
    public static void staticMethod() {
        System.out.println("Static method called");
    }
}
