package com.example.interfaces;

import java.util.List;

/**
 * Test interface for testing interface parsing
 */
public interface Interface {
    void doSomething();
    
    String getName();
    
    int calculate(int a, int b);
    
    List<String> getItems();
    
    default void defaultMethod() {
        System.out.println("Default method implementation");
    }
    
    static void staticInterfaceMethod() {
        System.out.println("Static interface method");
    }
}
