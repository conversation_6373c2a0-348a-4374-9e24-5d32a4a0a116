#!/usr/bin/env python3
"""
Test the proper tool implementation following HuggingFace documentation
"""

import os
import json

def test_tool_directly():
    """Test the tool directly without agent"""
    print("="*60)
    print("TESTING TOOL DIRECTLY")
    print("="*60)
    
    try:
        from java_tool_proper import java_parser_tool
        
        test_file = "test_java_files/Test1.java"
        print(f"Testing with file: {test_file}")
        
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
        
        # Call the tool directly
        result_json = java_parser_tool.forward(test_file)
        print("✅ Tool executed successfully")
        
        # Parse the JSON result
        result = json.loads(result_json)
        print(f"✅ JSON parsed successfully")
        print(f"   File: {result.get('file_path')}")
        print(f"   Classes: {len(result.get('classes', []))}")
        print(f"   Errors: {len(result.get('errors', []))}")
        
        if result.get('classes'):
            first_class = result['classes'][0]
            print(f"   First class: {first_class.get('class_name')}")
            print(f"   Methods: {len(first_class.get('methods', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_agent():
    """Test the tool with CodeAgent"""
    print("\n" + "="*60)
    print("TESTING WITH CODEAGENT")
    print("="*60)
    
    try:
        from smolagents import CodeAgent, HfApiModel
        from java_tool_proper import java_parser_tool
        
        print("✅ Imports successful")
        
        # Create agent following the exact pattern from documentation
        agent = CodeAgent(tools=[java_parser_tool], model=HfApiModel())
        print("✅ Agent created successfully")
        
        # Test with a simple prompt
        test_file = "test_java_files/Test1.java"
        prompt = f"Use the parse_java_file tool to analyze the file '{test_file}' and show me the results."
        
        print(f"📝 Prompt: {prompt}")
        print("🤖 Running agent...")
        
        response = agent.run(prompt)
        
        print("✅ Agent execution completed")
        print("📋 Agent Response:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_decorator_version():
    """Test the @tool decorator version"""
    print("\n" + "="*60)
    print("TESTING @tool DECORATOR VERSION")
    print("="*60)
    
    try:
        from smolagents import CodeAgent, HfApiModel
        from java_tool_proper import parse_java_file_decorator
        
        print("✅ Imports successful")
        
        # Create agent with decorator tool
        agent = CodeAgent(tools=[parse_java_file_decorator], model=HfApiModel())
        print("✅ Agent created with decorator tool")
        
        test_file = "test_java_files/Test1.java"
        prompt = f"Use the parse_java_file_decorator tool to analyze '{test_file}'."
        
        print(f"📝 Prompt: {prompt}")
        print("🤖 Running agent...")
        
        response = agent.run(prompt)
        
        print("✅ Decorator tool execution completed")
        print("📋 Response:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Decorator test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("PROPER TOOL IMPLEMENTATION TESTING")
    print("Following HuggingFace smolagents documentation")
    
    # Check HF_TOKEN
    hf_token = os.getenv('HF_TOKEN')
    if hf_token:
        print(f"✅ HF_TOKEN is set (length: {len(hf_token)})")
    else:
        print("⚠️  HF_TOKEN not set - agent tests may fail")
    
    tests = [
        ("Direct Tool Test", test_tool_directly),
        ("CodeAgent Test", test_with_agent),
        ("Decorator Tool Test", test_decorator_version),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed > 0:
        print("\n🎉 Proper tool implementation is working!")
        if passed == total:
            print("✨ All tests passed - your tool is ready for production!")
        else:
            print("⚠️  Some tests failed - check HF_TOKEN and network connection")
    else:
        print("\n❌ All tests failed - check implementation and dependencies")

if __name__ == "__main__":
    main()
