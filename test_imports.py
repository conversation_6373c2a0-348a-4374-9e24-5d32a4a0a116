#!/usr/bin/env python3
"""Test script to check imports"""

print("Testing imports...")

try:
    print("1. Testing standard library imports...")
    import sys, os, json, re
    print("   ✓ Standard library imports successful")
except Exception as e:
    print(f"   ❌ Standard library import error: {e}")

try:
    print("2. Testing javalang import...")
    import javalang
    print("   ✓ javalang imported successfully")
except Exception as e:
    print(f"   ❌ javalang import error: {e}")

try:
    print("3. Testing smolagents import...")
    import smolagents
    print("   ✓ smolagents imported successfully")
except Exception as e:
    print(f"   ❌ smolagents import error: {e}")

try:
    print("4. Testing javalang parsing...")
    content = """
    public class Test {
        public void method() {}
    }
    """
    tree = javalang.parse.parse(content)
    print("   ✓ javalang parsing successful")
except Exception as e:
    print(f"   ❌ javalang parsing error: {e}")

print("Import test complete!")
