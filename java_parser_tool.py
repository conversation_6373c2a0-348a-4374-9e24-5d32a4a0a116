"""
Java Parser Tool for AI-Powered Java Coding Assistant
Phase 0: Basic Java file parsing using javalang

This module provides a tool for parsing Java source files and extracting
structured information about classes and methods for RAG ingestion.
"""

import javalang
import os
from typing import Dict, List, Any

try:
    from smolagents import tool
    SMOLAGENTS_AVAILABLE = True
except ImportError:
    SMOLAGENTS_AVAILABLE = False
    # Define a dummy decorator if smolagents is not available
    def tool(func):
        return func


@tool
def parse_java_file(file_path: str) -> Dict[str, Any]:
    """
    Parses a Java file and extracts structured information about its classes and methods.
    
    This function uses javalang to parse Java source code and extract:
    - Class names and their methods
    - Package information
    - Import statements
    - Error information if parsing fails
    
    Args:
        file_path (str): Path to the Java source file to parse
        
    Returns:
        Dict[str, Any]: A dictionary containing:
            - file_path: The input file path
            - classes: List of class information dictionaries
            - package: Package name if present
            - imports: List of import statements
            - errors: List of any errors encountered during parsing
            
    Example:
        >>> result = parse_java_file("test_java_files/Test1.java")
        >>> print(result["classes"][0]["class_name"])
        Test1
    """
    parsed_data = {
        "file_path": file_path,
        "classes": [],
        "package": None,
        "imports": [],
        "errors": []
    }
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            parsed_data["errors"].append(f"File not found: {file_path}")
            return parsed_data
            
        # Read file content
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # Parse the Java content
        tree = javalang.parse.parse(content)
        
        # Extract package information
        if tree.package:
            parsed_data["package"] = tree.package.name
            
        # Extract import statements
        if tree.imports:
            parsed_data["imports"] = [imp.path for imp in tree.imports]
            
        # Extract class information
        for _, node in tree.filter(javalang.tree.ClassDeclaration):
            class_info = {
                "class_name": node.name,
                "methods": [],
                "fields": [],
                "modifiers": node.modifiers if node.modifiers else [],
                "extends": node.extends.name if node.extends else None,
                "implements": [impl.name for impl in node.implements] if node.implements else []
            }
            
            # Extract method information
            if hasattr(node, 'methods') and node.methods:
                for method_node in node.methods:
                    method_info = {
                        "name": method_node.name,
                        "modifiers": method_node.modifiers if method_node.modifiers else [],
                        "return_type": method_node.return_type.name if method_node.return_type else "void",
                        "parameters": []
                    }
                    
                    # Extract parameter information
                    if hasattr(method_node, 'parameters') and method_node.parameters:
                        for param in method_node.parameters:
                            param_info = {
                                "name": param.name,
                                "type": param.type.name if hasattr(param.type, 'name') else str(param.type)
                            }
                            method_info["parameters"].append(param_info)
                    
                    class_info["methods"].append(method_info)
            
            # Extract field information
            for field_decl in node.fields:
                for declarator in field_decl.declarators:
                    field_info = {
                        "name": declarator.name,
                        "type": field_decl.type.name if hasattr(field_decl.type, 'name') else str(field_decl.type),
                        "modifiers": field_decl.modifiers if field_decl.modifiers else []
                    }
                    class_info["fields"].append(field_info)
                    
            parsed_data["classes"].append(class_info)
            
        # Also extract interface declarations
        for _, node in tree.filter(javalang.tree.InterfaceDeclaration):
            interface_info = {
                "class_name": node.name,
                "type": "interface",
                "methods": [],
                "modifiers": node.modifiers if node.modifiers else [],
                "extends": [ext.name for ext in node.extends] if node.extends else []
            }
            
            # Extract interface method information
            if hasattr(node, 'methods') and node.methods:
                for method_node in node.methods:
                    method_info = {
                        "name": method_node.name,
                        "modifiers": method_node.modifiers if method_node.modifiers else [],
                        "return_type": method_node.return_type.name if method_node.return_type else "void",
                        "parameters": []
                    }
                    
                    # Extract parameter information
                    if hasattr(method_node, 'parameters') and method_node.parameters:
                        for param in method_node.parameters:
                            param_info = {
                                "name": param.name,
                                "type": param.type.name if hasattr(param.type, 'name') else str(param.type)
                            }
                            method_info["parameters"].append(param_info)
                    
                    interface_info["methods"].append(method_info)
                    
            parsed_data["classes"].append(interface_info)
            
    except FileNotFoundError:
        parsed_data["errors"].append(f"File not found: {file_path}")
    except javalang.parser.JavaSyntaxError as e:
        # Handle Java syntax errors gracefully
        location = "unknown location"
        if hasattr(e, 'at') and e.at:
            location = f"line {e.at.line}, column {e.at.column}"
        error_msg = f"Java syntax error in {file_path}: {e.description} at {location}"
        parsed_data["errors"].append(error_msg)
    except Exception as e:
        # Handle any other unexpected errors
        error_msg = f"An unexpected error occurred while parsing {file_path}: {str(e)}"
        parsed_data["errors"].append(error_msg)
    
    return parsed_data


def parse_java_file_simple(file_path: str) -> Dict[str, Any]:
    """
    Simplified version of parse_java_file that extracts only basic class and method names.
    This matches the Phase 0 requirements more closely.
    
    Args:
        file_path (str): Path to the Java source file to parse
        
    Returns:
        Dict[str, Any]: A dictionary containing file_path, classes, and errors
    """
    parsed_data = {
        "file_path": file_path,
        "classes": [],
        "errors": []
    }
    
    try:
        if not os.path.exists(file_path):
            parsed_data["errors"].append(f"File not found: {file_path}")
            return parsed_data
            
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        tree = javalang.parse.parse(content)
        
        # Extract class information (simplified)
        for _, node in tree.filter(javalang.tree.ClassDeclaration):
            class_info = {
                "class_name": node.name,
                "methods": []
            }
            
            # Extract method names only
            if hasattr(node, 'methods') and node.methods:
                for method_node in node.methods:
                    class_info["methods"].append(method_node.name)
                    
            parsed_data["classes"].append(class_info)
            
    except FileNotFoundError:
        parsed_data["errors"].append(f"File not found: {file_path}")
    except javalang.parser.JavaSyntaxError as e:
        location = "unknown location"
        if hasattr(e, 'at') and e.at:
            location = f"line {e.at.line}, column {e.at.column}"
        error_msg = f"Java syntax error in {file_path}: {e.description} at {location}"
        parsed_data["errors"].append(error_msg)
    except Exception as e:
        error_msg = f"An unexpected error occurred while parsing {file_path}: {str(e)}"
        parsed_data["errors"].append(error_msg)
    
    return parsed_data


if __name__ == "__main__":
    # Test the parser with a simple example
    import sys
    if len(sys.argv) > 1:
        result = parse_java_file(sys.argv[1])
        import json
        print(json.dumps(result, indent=2))
    else:
        print("Usage: python java_parser_tool.py <java_file_path>")
