"""
Java Parser Tool for AI-Powered Java Coding Assistant
Phase 0: Basic Java file parsing using simple_parser

This module provides a tool for parsing Java source files and extracting
structured information about classes and methods for RAG ingestion.
"""

import os
from typing import Dict, List, Any
from simple_parser import SimpleJavaParser

try:
    from smolagents import tool
    SMOLAGENTS_AVAILABLE = True
except ImportError:
    SMOLAGENTS_AVAILABLE = False
    # Define a dummy decorator if smolagents is not available
    def tool(func):
        return func


@tool
def parse_java_file(file_path: str) -> Dict[str, Any]:
    """
    Parses a Java file and extracts structured information about its classes and methods.

    This function uses simple_parser to parse Java source code and extract:
    - Class names and their methods
    - Package information
    - Import statements
    - Error information if parsing fails

    Args:
        file_path (str): Path to the Java source file to parse

    Returns:
        Dict[str, Any]: A dictionary containing:
            - file_path: The input file path
            - classes: List of class information dictionaries
            - package: Package name if present
            - imports: List of import statements
            - errors: List of any errors encountered during parsing

    Example:
        >>> result = parse_java_file("test_java_files/Test1.java")
        >>> print(result["classes"][0]["class_name"])
        Test1
    """
    # Use the SimpleJavaParser to parse the file
    parser = SimpleJavaParser()
    result = parser.parse_file(file_path)

    # Convert the simple parser output to match the expected format
    # The simple parser already returns the correct structure
    return result


def parse_java_file_simple(file_path: str) -> Dict[str, Any]:
    """
    Simplified version of parse_java_file that extracts only basic class and method names.
    This matches the Phase 0 requirements more closely.

    Args:
        file_path (str): Path to the Java source file to parse

    Returns:
        Dict[str, Any]: A dictionary containing file_path, classes, and errors
    """
    # Use the SimpleJavaParser for simplified parsing
    parser = SimpleJavaParser()
    result = parser.parse_file(file_path)

    # Convert to simplified format (only class names and method names)
    simplified_result = {
        "file_path": result["file_path"],
        "classes": [],
        "errors": result["errors"]
    }

    for class_info in result.get("classes", []):
        simplified_class = {
            "class_name": class_info["class_name"],
            "methods": class_info["methods"]
        }
        simplified_result["classes"].append(simplified_class)

    return simplified_result


if __name__ == "__main__":
    # Test the parser with a simple example
    import sys
    if len(sys.argv) > 1:
        result = parse_java_file(sys.argv[1])
        import json
        print(json.dumps(result, indent=2))
    else:
        print("Usage: python java_parser_tool.py <java_file_path>")
