#!/usr/bin/env python3
"""
Test CodeAgent with model - focused test for the selected line:
agent = CodeAgent(model=model, tools=[parse_java_file])
"""

import os
import sys
import json

def test_codeagent_with_model():
    """Test the exact CodeAgent setup from main.py"""
    print("="*60)
    print("TESTING CODEAGENT WITH MODEL")
    print("="*60)
    
    # Step 1: Check HF_TOKEN
    print("1. Checking HF_TOKEN...")
    hf_token = os.getenv('HF_TOKEN')
    if hf_token:
        print(f"✅ HF_TOKEN is set (length: {len(hf_token)})")
    else:
        print("⚠️  HF_TOKEN not set - will use mock/fallback")
        print("   To set: export HF_TOKEN='your_hf_token_here'")
    
    # Step 2: Import dependencies
    print("\n2. Importing dependencies...")
    try:
        from smolagents import CodeAgent, HfApiModel
        from java_parser_tool import parse_java_file
        print("✅ All imports successful")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Step 3: Initialize model
    print("\n3. Initializing model...")
    try:
        model_name = "mistralai/Mistral-7B-Instruct-v0.2"
        print(f"   Model: {model_name}")
        model = HfApiModel(model_name)
        print("✅ Model initialized successfully")
        print(f"   Model type: {type(model)}")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return False
    
    # Step 4: Create CodeAgent (the selected line)
    print("\n4. Creating CodeAgent with model and tools...")
    try:
        print("   Executing: agent = CodeAgent(model=model, tools=[parse_java_file])")
        agent = CodeAgent(model=model, tools=[parse_java_file])
        print("✅ CodeAgent created successfully")
        print(f"   Agent type: {type(agent)}")
        
        # Check agent properties
        if hasattr(agent, 'tools'):
            print(f"   Tools count: {len(agent.tools)}")
        if hasattr(agent, 'model'):
            print(f"   Model attached: {type(agent.model)}")
            
    except Exception as e:
        print(f"❌ CodeAgent creation failed: {e}")
        print(f"   Error type: {type(e)}")
        return False
    
    # Step 5: Test agent execution
    print("\n5. Testing agent execution...")
    try:
        test_file = "test_java_files/Test1.java"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
            
        print(f"   Test file: {test_file}")
        
        # Create prompt
        prompt = f"Execute the 'parse_java_file' tool with the file_path '{test_file}'. Return the direct output of this tool."
        print(f"   Prompt: {prompt[:50]}...")
        
        print("   Running agent...")
        response = agent.run(prompt)
        
        print("✅ Agent execution completed")
        print(f"   Response type: {type(response)}")
        print(f"   Response length: {len(str(response))}")
        
        # Show response preview
        response_str = str(response)
        if len(response_str) > 200:
            print(f"   Response preview: {response_str[:200]}...")
        else:
            print(f"   Response: {response_str}")
        
        # Try to parse as JSON
        try:
            if isinstance(response, str):
                json_data = json.loads(response)
                print("✅ Response is valid JSON")
                print(f"   Classes found: {len(json_data.get('classes', []))}")
                if json_data.get('classes'):
                    first_class = json_data['classes'][0]
                    print(f"   First class: {first_class.get('class_name', 'Unknown')}")
                    print(f"   Methods: {len(first_class.get('methods', []))}")
            else:
                print("ℹ️  Response is not a string")
        except json.JSONDecodeError:
            print("ℹ️  Response contains non-JSON content (normal for conversational models)")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent execution failed: {e}")
        print(f"   Error type: {type(e)}")
        return False

def test_fallback_comparison():
    """Compare with fallback mode"""
    print("\n" + "="*60)
    print("COMPARING WITH FALLBACK MODE")
    print("="*60)
    
    try:
        from java_parser_tool import parse_java_file
        
        test_file = "test_java_files/Test1.java"
        print(f"Testing fallback with: {test_file}")
        
        result = parse_java_file(test_file)
        print("✅ Fallback mode successful")
        print(f"   Classes found: {len(result.get('classes', []))}")
        print(f"   Errors: {len(result.get('errors', []))}")
        
        if result.get('classes'):
            first_class = result['classes'][0]
            print(f"   First class: {first_class.get('class_name', 'Unknown')}")
            print(f"   Methods: {len(first_class.get('methods', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback test failed: {e}")
        return False

def test_different_models():
    """Test with different models"""
    print("\n" + "="*60)
    print("TESTING DIFFERENT MODELS")
    print("="*60)
    
    models_to_test = [
        "mistralai/Mistral-7B-Instruct-v0.2",
        "microsoft/DialoGPT-medium",
        "HuggingFaceH4/zephyr-7b-beta"
    ]
    
    try:
        from smolagents import CodeAgent, HfApiModel
        from java_parser_tool import parse_java_file
        
        for model_name in models_to_test:
            print(f"\nTesting model: {model_name}")
            try:
                model = HfApiModel(model_name)
                agent = CodeAgent(model=model, tools=[parse_java_file])
                print(f"✅ {model_name} - Agent created successfully")
            except Exception as e:
                print(f"❌ {model_name} - Failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return False

def main():
    """Run all tests"""
    print("CODEAGENT MODEL TESTING")
    print("Testing the selected line: agent = CodeAgent(model=model, tools=[parse_java_file])")
    
    tests = [
        ("CodeAgent with Model", test_codeagent_with_model),
        ("Fallback Comparison", test_fallback_comparison),
        ("Different Models", test_different_models),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed > 0:
        print("\n🎉 CodeAgent integration is working!")
        print("\nTo run with your setup:")
        print("1. Set HF_TOKEN: export HF_TOKEN='your_token'")
        print("2. Run: python main.py test_java_files/Test1.java")
        print("3. Or test specific model: python main.py --model 'model_name' test_java_files/Test1.java")
    else:
        print("\n⚠️  CodeAgent has issues - check dependencies and HF_TOKEN")
    
    return passed > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
