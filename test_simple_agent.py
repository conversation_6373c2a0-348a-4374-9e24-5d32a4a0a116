#!/usr/bin/env python3
"""
Simple test of smolagents with DuckDuckGo search tool
"""

import os

def test_simple_agent():
    """Test the exact example you provided"""
    print("="*60)
    print("TESTING SIMPLE SMOLAGENTS EXAMPLE")
    print("="*60)
    
    # Check HF_TOKEN
    hf_token = os.getenv('HF_TOKEN')
    if hf_token:
        print(f"✅ HF_TOKEN is set (length: {len(hf_token)})")
    else:
        print("⚠️  HF_TOKEN not set - may cause issues")
        print("   Set with: export HF_TOKEN='your_token_here'")
    
    try:
        print("\n1. Importing smolagents...")
        from smolagents import CodeAgent, DuckDuckGoSearchTool, HfApiModel
        print("✅ Imports successful")
        
        print("\n2. Creating agent...")
        agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=HfApiModel())
        print("✅ Agent created successfully")
        print(f"   Agent type: {type(agent)}")
        
        print("\n3. Running agent with question...")
        question = "How many seconds would it take for a leopard at full speed to run through Pont des Arts?"
        print(f"   Question: {question}")
        
        print("\n   Calling agent.run()...")
        response = agent.run(question)
        
        print("✅ Agent execution completed")
        print(f"   Response type: {type(response)}")
        print(f"   Response length: {len(str(response))}")
        
        print("\n4. Agent Response:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print(f"   Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_with_our_tool():
    """Test with our Java parsing tool instead"""
    print("\n" + "="*60)
    print("TESTING WITH OUR JAVA PARSING TOOL")
    print("="*60)
    
    try:
        from smolagents import CodeAgent, HfApiModel
        from java_parser_tool import parse_java_file
        
        print("✅ Imports successful")
        
        print("Creating agent with our tool...")
        agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())
        print("✅ Agent created")
        
        print("Running agent with Java file...")
        response = agent.run("Use parse_java_file to analyze 'test_java_files/Test1.java'")
        
        print("✅ Agent execution completed")
        print("Response:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Run both tests"""
    print("SIMPLE SMOLAGENTS TESTING")
    print("Testing the exact example you provided")
    
    # Test 1: Simple DuckDuckGo example
    simple_works = test_simple_agent()
    
    # Test 2: Our Java tool
    java_works = test_with_our_tool()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    if simple_works:
        print("✅ Simple DuckDuckGo example: WORKS")
    else:
        print("❌ Simple DuckDuckGo example: FAILED")
    
    if java_works:
        print("✅ Java parsing tool example: WORKS")
    else:
        print("❌ Java parsing tool example: FAILED")
    
    if simple_works or java_works:
        print("\n🎉 At least one test worked - smolagents is functional!")
    else:
        print("\n⚠️  Both tests failed - check HF_TOKEN and network connection")
    
    print("\n💡 If tests fail, try:")
    print("1. Set HF_TOKEN: export HF_TOKEN='your_token'")
    print("2. Check internet connection")
    print("3. Try different model: HfApiModel('microsoft/DialoGPT-medium')")

if __name__ == "__main__":
    main()
