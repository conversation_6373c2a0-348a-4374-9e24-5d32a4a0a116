#!/usr/bin/env python3
"""
Basic test script to verify the project structure and basic functionality
without external dependencies.
"""

import os
import sys
import json

def test_project_structure():
    """Test that all required files are present."""
    print("Testing project structure...")
    
    required_files = [
        "main.py",
        "java_parser_tool.py",
        "requirements.txt",
        "README.md",
        ".gitignore",
        "test_java_files/Test1.java",
        "test_java_files/Test2.java",
        "test_java_files/Empty.java",
        "test_java_files/Broken.java",
        "test_java_files/Interface.java"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    else:
        print("\n✅ All required files present")
        return True

def test_java_files():
    """Test that Java test files are valid."""
    print("\nTesting Java test files...")
    
    java_files = [
        "test_java_files/Test1.java",
        "test_java_files/Test2.java", 
        "test_java_files/Empty.java",
        "test_java_files/Interface.java"
    ]
    
    for java_file in java_files:
        try:
            with open(java_file, 'r') as f:
                content = f.read()
                if content.strip():
                    print(f"✓ {java_file} - {len(content)} characters")
                else:
                    print(f"⚠ {java_file} - empty file")
        except Exception as e:
            print(f"❌ {java_file} - error: {e}")
            return False
    
    # Test the broken file separately
    try:
        with open("test_java_files/Broken.java", 'r') as f:
            content = f.read()
            print(f"✓ test_java_files/Broken.java - {len(content)} characters (intentionally broken)")
    except Exception as e:
        print(f"❌ test_java_files/Broken.java - error: {e}")
        return False
    
    print("✅ All Java test files readable")
    return True

def test_python_imports():
    """Test Python imports and dependencies."""
    print("\nTesting Python imports...")
    
    # Test standard library imports
    try:
        import sys, os, json, argparse, re
        print("✓ Standard library imports successful")
    except Exception as e:
        print(f"❌ Standard library import error: {e}")
        return False
    
    # Test our modules
    try:
        sys.path.insert(0, '.')
        import java_parser_tool
        print("✓ java_parser_tool module imported")
    except Exception as e:
        print(f"⚠ java_parser_tool import error: {e}")
        print("  This is expected if javalang is not installed")
    
    try:
        import main
        print("✓ main module imported")
    except Exception as e:
        print(f"⚠ main module import error: {e}")
        print("  This is expected if dependencies are not installed")
    
    return True

def test_output_directory():
    """Test output directory creation."""
    print("\nTesting output directory...")
    
    output_dir = "output"
    try:
        os.makedirs(output_dir, exist_ok=True)
        if os.path.exists(output_dir) and os.path.isdir(output_dir):
            print(f"✓ Output directory created: {output_dir}")
            return True
        else:
            print(f"❌ Failed to create output directory: {output_dir}")
            return False
    except Exception as e:
        print(f"❌ Error creating output directory: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    print("\nChecking dependencies...")
    
    dependencies = {
        'javalang': 'Java parsing library',
        'smolagents': 'AI agent framework',
        'huggingface_hub': 'Hugging Face API integration'
    }
    
    available = []
    missing = []
    
    for dep, description in dependencies.items():
        try:
            __import__(dep)
            available.append(f"{dep} ({description})")
        except ImportError:
            missing.append(f"{dep} ({description})")
    
    if available:
        print("✅ Available dependencies:")
        for dep in available:
            print(f"  ✓ {dep}")
    
    if missing:
        print("⚠ Missing dependencies:")
        for dep in missing:
            print(f"  ❌ {dep}")
        print("\nTo install missing dependencies, run:")
        print("  pip install -r requirements.txt")
        print("\nOr install individually:")
        for dep_name in [d.split(' ')[0] for d in missing]:
            print(f"  pip install {dep_name}")
    
    return len(missing) == 0

def main():
    """Run all tests."""
    print("="*60)
    print("AI-Powered Java Coding Assistant - Phase 0 Test Suite")
    print("="*60)
    
    tests = [
        test_project_structure,
        test_java_files,
        test_python_imports,
        test_output_directory
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Check dependencies (informational, doesn't affect pass/fail)
    check_dependencies()
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("\nProject structure is ready for Phase 0 implementation.")
        print("Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Set HF_TOKEN environment variable (optional)")
        print("3. Run: python main.py test_java_files/Test1.java")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("Please fix the issues above before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
