#!/usr/bin/env python3
"""
Proper Java Parser Tool implementation following HuggingFace smolagents documentation
https://huggingface.co/docs/smolagents/v1.17.0/en/tutorials/tools
"""

from smolagents import Tool
from typing import Dict, Any
import os
import json


class JavaParserTool(Tool):
    name = "parse_java_file"
    description = """
    This tool parses a Java source file and extracts structured information about its classes and methods.
    It returns detailed information including class names, method names, package information, and any parsing errors.
    The output is formatted as JSON suitable for further processing or RAG ingestion.
    """
    inputs = {
        "file_path": {
            "type": "string", 
            "description": "The path to the Java source file to parse (e.g., 'test_java_files/Test1.java')"
        }
    }
    output_type = "string"  # JSON string
    
    def forward(self, file_path: str) -> str:
        """Parse Java file and return JSON string with results"""
        # Import here as required by the documentation
        from simple_parser import SimpleJavaParser
        
        # Use the SimpleJavaParser to parse the file
        parser = SimpleJavaParser()
        result = parser.parse_file(file_path)
        
        # Return as JSON string
        return json.dumps(result, indent=2)


# Create the tool instance
java_parser_tool = JavaParserTool()


# Also provide the @tool decorator version for compatibility
try:
    from smolagents import tool
    
    @tool
    def parse_java_file_decorator(file_path: str) -> str:
        """
        Parses a Java file and extracts structured information about its classes and methods.
        
        Args:
            file_path (str): Path to the Java source file to parse
            
        Returns:
            str: JSON string containing file_path, classes, package, imports, and errors
        """
        from simple_parser import SimpleJavaParser
        
        parser = SimpleJavaParser()
        result = parser.parse_file(file_path)
        return json.dumps(result, indent=2)
        
except ImportError:
    # Fallback if smolagents not available
    def parse_java_file_decorator(file_path: str) -> str:
        from simple_parser import SimpleJavaParser
        parser = SimpleJavaParser()
        result = parser.parse_file(file_path)
        return json.dumps(result, indent=2)


if __name__ == "__main__":
    # Test the tool
    import sys
    if len(sys.argv) > 1:
        result = java_parser_tool.forward(sys.argv[1])
        print("Tool Result:")
        print(result)
    else:
        print("Usage: python java_tool_proper.py <java_file_path>")
        print("Example: python java_tool_proper.py test_java_files/Test1.java")
