#!/usr/bin/env python3
"""
Working version that simulates AI agent behavior without API issues
This demonstrates how the system would work with a functioning AI model
"""

import sys
import json
import os
import argparse
from typing import Dict, Any

# Import our Java parser tool
try:
    from java_parser_tool import parse_java_file, parse_java_file_simple
except ImportError:
    # Fallback if import fails
    from simple_parser import SimpleJavaParser

    def parse_java_file(file_path):
        parser = SimpleJavaParser()
        return parser.parse_file(file_path)

    def parse_java_file_simple(file_path):
        return parse_java_file(file_path)


class WorkingAIAgent:
    """A working AI agent simulation that demonstrates proper tool usage"""

    def __init__(self, model_name: str):
        self.model_name = model_name
        self.tools = {}

    def add_tool(self, tool_func):
        """Add a tool to the agent"""
        self.tools[tool_func.__name__] = tool_func

    def run(self, prompt: str) -> str:
        """Simulate AI agent that properly calls tools and returns JSON"""

        # Parse the prompt to understand what's being requested
        if "parse_java_file" in prompt and "file_path=" in prompt:
            # Extract file path from prompt
            import re
            file_match = re.search(r"file_path[=\s]*['\"]([^'\"]+)['\"]", prompt)
            if file_match:
                file_path = file_match.group(1)

                # Call the tool directly (simulating what AI would do)
                if "parse_java_file" in self.tools:
                    result = self.tools["parse_java_file"](file_path)

                    # Return as JSON string (simulating AI response)
                    return json.dumps(result, indent=2)

        # Fallback response
        return '{"error": "Could not understand the request"}'


def setup_argument_parser():
    """Set up command-line argument parsing."""
    parser = argparse.ArgumentParser(
        description="AI-Powered Java Coding Assistant - Working Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_working.py test_java_files/Test1.java
  python main_working.py --use-real-ai test_java_files/Test1.java
  python main_working.py --output custom.json test_java_files/Test1.java
        """
    )

    parser.add_argument(
        "java_file",
        help="Path to the Java file to analyze"
    )

    parser.add_argument(
        "--output", "-o",
        help="Output file path (default: output/<filename>_summary.json)"
    )

    parser.add_argument(
        "--use-real-ai",
        action="store_true",
        help="Try to use real AI (may fail with API errors)"
    )

    parser.add_argument(
        "--model",
        default="mistralai/Mistral-7B-Instruct-v0.2",
        help="Model name (for real AI mode)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )

    return parser


def create_output_filename(java_file_path: str, custom_output: str = None) -> str:
    """Create an appropriate output filename."""
    if custom_output:
        return custom_output

    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # Generate filename based on input file
    base_filename = os.path.basename(java_file_path)
    name_without_ext = os.path.splitext(base_filename)[0]
    return os.path.join(output_dir, f"{name_without_ext}_working_summary.json")


def run_with_working_ai(java_file_path: str, model_name: str, verbose: bool = False) -> Dict[str, Any]:
    """Run with working AI agent simulation"""
    if verbose:
        print(f"🤖 Running with Working AI Agent (simulated {model_name})")

    # Create working AI agent
    agent = WorkingAIAgent(model_name)
    agent.add_tool(parse_java_file)

    # Create prompt
    prompt = f"""I need you to analyze a Java file. Please use the parse_java_file tool to analyze the file at path '{java_file_path}'.

Call the tool like this: parse_java_file(file_path='{java_file_path}')

Return only the JSON output from the tool without any additional text or explanation."""

    if verbose:
        print("📝 Sending prompt to AI agent...")
        print(f"   Prompt: {prompt[:100]}...")

    try:
        # Run the agent
        response = agent.run(prompt)

        if verbose:
            print("✅ AI agent responded successfully")
            print(f"   Response type: {type(response)}")
            print(f"   Response length: {len(response)}")

        # Parse the response
        result = json.loads(response)

        if verbose:
            print("✅ AI response parsed as JSON")
            print(f"   Classes found: {len(result.get('classes', []))}")

        # Add metadata about the AI processing
        result["ai_agent"] = {
            "model": model_name,
            "status": "success",
            "method": "working_simulation"
        }

        return result

    except Exception as e:
        if verbose:
            print(f"❌ AI agent failed: {e}")

        # Fallback to direct tool call
        result = parse_java_file(java_file_path)
        result["ai_agent"] = {
            "model": model_name,
            "status": "failed",
            "error": str(e),
            "fallback": "direct_tool_call"
        }
        return result


def run_with_real_ai(java_file_path: str, model_name: str, verbose: bool = False) -> Dict[str, Any]:
    """Try to run with real AI (may fail)"""
    try:
        from smolagents import CodeAgent
        try:
            from smolagents import InferenceClientModel as ModelClass
        except ImportError:
            from smolagents import HfApiModel as ModelClass

        if verbose:
            print(f"🌐 Attempting real AI with {model_name}")

        # Initialize real AI
        model = ModelClass(model_name)
        agent = CodeAgent(model=model, tools=[parse_java_file])

        prompt = f"""I need you to analyze a Java file. Please use the parse_java_file tool to analyze the file at path '{java_file_path}'.

Call the tool like this: parse_java_file(file_path='{java_file_path}')

Return only the JSON output from the tool without any additional text or explanation."""

        if verbose:
            print("📡 Calling real AI API...")

        response = agent.run(prompt)

        if response is None:
            raise Exception("AI returned None response")

        # Try to parse response
        response_str = str(response)
        result = json.loads(response_str)

        if verbose:
            print("✅ Real AI succeeded!")

        result["ai_agent"] = {
            "model": model_name,
            "status": "success",
            "method": "real_api"
        }

        return result

    except Exception as e:
        if verbose:
            print(f"❌ Real AI failed: {e}")
            print("🔄 Falling back to working AI simulation...")

        # Fall back to working AI
        return run_with_working_ai(java_file_path, model_name, verbose)


def save_output(data: Dict[str, Any], output_path: str, verbose: bool = False) -> None:
    """Save the analysis results to a JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        if verbose:
            print(f"💾 Results saved to: {output_path}")
        else:
            print(f"Analysis complete. Results saved to: {output_path}")

    except Exception as e:
        print(f"Error saving output to {output_path}: {e}")


def print_summary(data: Dict[str, Any]) -> None:
    """Print a brief summary of the analysis results."""
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)

    print(f"File: {data.get('file_path', 'Unknown')}")

    # Show AI agent info if available
    ai_info = data.get('ai_agent', {})
    if ai_info:
        print(f"AI Agent: {ai_info.get('model', 'Unknown')} ({ai_info.get('status', 'Unknown')})")
        if ai_info.get('method'):
            print(f"Method: {ai_info['method']}")

    if data.get('errors'):
        print(f"Errors: {len(data['errors'])}")
        for error in data['errors']:
            print(f"  - {error}")
    else:
        print("Errors: None")

    classes = data.get('classes', [])
    print(f"Classes found: {len(classes)}")

    for class_info in classes:
        class_name = class_info.get('class_name', 'Unknown')
        methods = class_info.get('methods', [])
        if isinstance(methods, list) and methods:
            method_count = len(methods)
            method_names = methods[:3] if isinstance(methods[0], str) else [m.get('name', 'Unknown') for m in methods[:3]]

            print(f"  - {class_name}: {method_count} methods")
            if method_names:
                print(f"    Methods: {', '.join(method_names)}")
                if method_count > 3:
                    print(f"    ... and {method_count - 3} more")
        else:
            print(f"  - {class_name}: No methods")


def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.java_file):
        print(f"Error: File not found: {args.java_file}")
        sys.exit(1)

    if not args.java_file.endswith('.java'):
        print(f"Warning: File does not have .java extension: {args.java_file}")

    # Determine output file path
    output_path = create_output_filename(args.java_file, args.output)

    try:
        # Run analysis
        if args.use_real_ai:
            output_data = run_with_real_ai(args.java_file, args.model, args.verbose)
        else:
            output_data = run_with_working_ai(args.java_file, args.model, args.verbose)

        # Save results
        save_output(output_data, output_path, args.verbose)

        # Print summary
        print_summary(output_data)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
