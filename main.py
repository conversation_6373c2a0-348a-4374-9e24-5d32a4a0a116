#!/usr/bin/env python3
"""
AI-Powered Java Coding Assistant - Phase 0
Main execution script using smolagents

This script provides a command-line interface for analyzing Java files
using an AI agent powered by smolagents and javalang.
"""

import sys
import json
import os
import argparse
from typing import Dict, Any

# Try to import smolagents components
try:
    from smolagents import CodeAgent, HfApiModel
    SMOLAGENTS_AVAILABLE = True
except ImportError:
    SMOLAGENTS_AVAILABLE = False
    print("Warning: smolagents not available. Running in fallback mode.")

# Import our Java parser tool
from java_parser_tool import parse_java_file, parse_java_file_simple


def setup_argument_parser():
    """Set up command-line argument parsing."""
    parser = argparse.ArgumentParser(
        description="AI-Powered Java Coding Assistant - Phase 0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py test_java_files/Test1.java
  python main.py --output custom_output.json test_java_files/Test2.java
  python main.py --fallback test_java_files/Test1.java
        """
    )
    
    parser.add_argument(
        "java_file",
        help="Path to the Java file to analyze"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file path (default: output/<filename>_summary.json)"
    )
    
    parser.add_argument(
        "--fallback",
        action="store_true",
        help="Use fallback mode without smolagents"
    )
    
    parser.add_argument(
        "--model",
        default="mistralai/Mistral-7B-Instruct-v0.2",
        help="Hugging Face model to use (default: mistralai/Mistral-7B-Instruct-v0.2)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    return parser


def create_output_filename(java_file_path: str, custom_output: str = None) -> str:
    """Create an appropriate output filename."""
    if custom_output:
        return custom_output
        
    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename based on input file
    base_filename = os.path.basename(java_file_path)
    name_without_ext = os.path.splitext(base_filename)[0]
    return os.path.join(output_dir, f"{name_without_ext}_summary.json")


def run_with_smolagents(java_file_path: str, model_name: str, verbose: bool = False) -> Dict[str, Any]:
    """Run the analysis using smolagents AI agent."""
    if not SMOLAGENTS_AVAILABLE:
        raise ImportError("smolagents is not available")
    
    if verbose:
        print(f"Initializing AI model: {model_name}")
    
    # Check for HF_TOKEN environment variable
    hf_token = os.getenv('HF_TOKEN')
    if not hf_token:
        print("Warning: HF_TOKEN environment variable not set.")
        print("You may need to set it for Hugging Face API access:")
        print("export HF_TOKEN='your_hf_api_token_here'")
    
    try:
        # Initialize the language model
        model = HfApiModel(model_name)
        
        # Initialize the CodeAgent with our parsing tool
        agent = CodeAgent(model=model, tools=[parse_java_file])
        
        if verbose:
            print(f"Analyzing {java_file_path} with AI agent...")
        
        # Create a prompt for the agent
        prompt = f"""
        Please analyze the Java file located at '{java_file_path}'. 
        Use the 'parse_java_file' tool to extract information about its classes and methods. 
        Return only the structured JSON output from the tool, without any additional commentary.
        """
        
        # Run the agent
        agent_response = agent.run(prompt)
        
        if verbose:
            print("Agent Response (raw):")
            print(agent_response)
        
        # Try to parse the agent's response as JSON
        try:
            output_data = json.loads(agent_response)
            return output_data
        except json.JSONDecodeError:
            if verbose:
                print("Warning: Could not decode JSON directly from agent response.")
                print("Attempting to extract JSON or fall back to direct tool call.")
            
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', agent_response, re.DOTALL)
            if json_match:
                try:
                    output_data = json.loads(json_match.group())
                    return output_data
                except json.JSONDecodeError:
                    pass
            
            # Fallback: call the tool directly
            if verbose:
                print("Falling back to direct tool call...")
            output_data = parse_java_file(java_file_path)
            output_data["agent_response"] = agent_response
            return output_data
            
    except Exception as e:
        if verbose:
            print(f"Error with smolagents: {e}")
            print("Falling back to direct tool call...")
        
        # Fallback to direct tool call
        output_data = parse_java_file(java_file_path)
        output_data["smolagents_error"] = str(e)
        return output_data


def run_fallback_mode(java_file_path: str, verbose: bool = False) -> Dict[str, Any]:
    """Run the analysis in fallback mode without smolagents."""
    if verbose:
        print(f"Running in fallback mode for {java_file_path}")
    
    return parse_java_file_simple(java_file_path)


def save_output(data: Dict[str, Any], output_path: str, verbose: bool = False) -> None:
    """Save the analysis results to a JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        if verbose:
            print(f"Results saved to: {output_path}")
        else:
            print(f"Analysis complete. Results saved to: {output_path}")
            
    except Exception as e:
        print(f"Error saving output to {output_path}: {e}")


def print_summary(data: Dict[str, Any]) -> None:
    """Print a brief summary of the analysis results."""
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    
    print(f"File: {data.get('file_path', 'Unknown')}")
    
    if data.get('errors'):
        print(f"Errors: {len(data['errors'])}")
        for error in data['errors']:
            print(f"  - {error}")
    else:
        print("Errors: None")
    
    classes = data.get('classes', [])
    print(f"Classes found: {len(classes)}")
    
    for class_info in classes:
        class_name = class_info.get('class_name', 'Unknown')
        methods = class_info.get('methods', [])
        if isinstance(methods, list) and methods:
            if isinstance(methods[0], str):
                method_count = len(methods)
                method_names = methods[:3]  # Show first 3 methods
            else:
                method_count = len(methods)
                method_names = [m.get('name', 'Unknown') for m in methods[:3]]
            
            print(f"  - {class_name}: {method_count} methods")
            if method_names:
                print(f"    Methods: {', '.join(method_names)}")
                if method_count > 3:
                    print(f"    ... and {method_count - 3} more")
        else:
            print(f"  - {class_name}: No methods")


def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.java_file):
        print(f"Error: File not found: {args.java_file}")
        sys.exit(1)
    
    if not args.java_file.endswith('.java'):
        print(f"Warning: File does not have .java extension: {args.java_file}")
    
    # Determine output file path
    output_path = create_output_filename(args.java_file, args.output)
    
    try:
        # Run analysis
        if args.fallback or not SMOLAGENTS_AVAILABLE:
            output_data = run_fallback_mode(args.java_file, args.verbose)
        else:
            output_data = run_with_smolagents(args.java_file, args.model, args.verbose)
        
        # Save results
        save_output(output_data, output_path, args.verbose)
        
        # Print summary
        print_summary(output_data)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
