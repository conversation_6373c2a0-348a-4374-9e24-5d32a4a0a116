#!/usr/bin/env python3
"""
Java parsing using exact DuckDuckGo pattern:
from smolagents import CodeAgent, DuckDuckGoSearchTool, HfApiModel
agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=HfApiModel())
"""

import sys
import json
import os
import argparse
from typing import Dict, Any

# Import our Java parser tool
from java_parser_tool import parse_java_file


def setup_argument_parser():
    """Set up command-line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Java Parsing with DuckDuckGo Pattern",
        epilog="Uses: agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())"
    )
    
    parser.add_argument("java_file", help="Path to the Java file to analyze")
    parser.add_argument("--output", "-o", help="Output file path")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    
    return parser


def create_output_filename(java_file_path: str, custom_output: str = None) -> str:
    """Create an appropriate output filename."""
    if custom_output:
        return custom_output
        
    os.makedirs("output", exist_ok=True)
    base_filename = os.path.basename(java_file_path)
    name_without_ext = os.path.splitext(base_filename)[0]
    return os.path.join("output", f"{name_without_ext}_duckduckgo_pattern.json")


def run_with_duckduckgo_pattern(java_file_path: str, verbose: bool = False) -> Dict[str, Any]:
    """Run using exact DuckDuckGo pattern but with our Java tool"""
    
    if verbose:
        print("🦆 Using DuckDuckGo Pattern for Java Analysis")
        print("Pattern: agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())")
    
    try:
        if verbose:
            print("Step 1: Importing smolagents...")
        
        # Exact same imports as DuckDuckGo example (but without DuckDuckGoSearchTool)
        from smolagents import CodeAgent, HfApiModel
        
        if verbose:
            print("✅ Imports successful")
            print("Step 2: Creating agent with exact DuckDuckGo pattern...")
        
        # Exact same pattern as DuckDuckGo example:
        # agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=HfApiModel())
        # But using our Java parsing tool instead:
        agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())
        
        if verbose:
            print("✅ Agent created successfully")
            print(f"   Agent type: {type(agent)}")
            print("Step 3: Running agent...")
        
        # Create prompt for Java analysis
        prompt = f"Use the parse_java_file tool to analyze '{java_file_path}' and return the JSON result."
        
        if verbose:
            print(f"   Prompt: {prompt}")
            print("   Calling agent.run()...")
        
        # Run the agent (same as DuckDuckGo example)
        response = agent.run(prompt)
        
        if verbose:
            print("✅ Agent execution completed")
            print(f"   Response type: {type(response)}")
            print(f"   Response length: {len(str(response)) if response else 0}")
        
        # Process response
        if response is None:
            if verbose:
                print("⚠️  Agent returned None, using fallback")
            result = parse_java_file(java_file_path)
            result["agent_status"] = "returned_none"
            return result
        
        # Try to parse as JSON
        response_str = str(response)
        try:
            result = json.loads(response_str)
            if verbose:
                print("✅ Response parsed as JSON successfully")
            result["agent_status"] = "success"
            return result
        except json.JSONDecodeError:
            if verbose:
                print("⚠️  Response not JSON, extracting or using fallback")
            
            # Try to extract JSON
            import re
            json_match = re.search(r'\{.*\}', response_str, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    result["agent_status"] = "extracted_json"
                    return result
                except json.JSONDecodeError:
                    pass
            
            # Fallback
            result = parse_java_file(java_file_path)
            result["agent_status"] = "fallback"
            result["agent_response"] = response_str[:200] + "..." if len(response_str) > 200 else response_str
            return result
            
    except Exception as e:
        if verbose:
            print(f"❌ DuckDuckGo pattern failed: {e}")
            print("   Using direct tool call fallback...")
        
        # Fallback to direct tool call
        result = parse_java_file(java_file_path)
        result["agent_status"] = "error"
        result["agent_error"] = str(e)
        return result


def save_output(data: Dict[str, Any], output_path: str, verbose: bool = False) -> None:
    """Save the analysis results to a JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        print(f"Analysis complete. Results saved to: {output_path}")
            
    except Exception as e:
        print(f"Error saving output to {output_path}: {e}")


def print_summary(data: Dict[str, Any]) -> None:
    """Print a brief summary of the analysis results."""
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY (DuckDuckGo Pattern)")
    print("="*50)
    
    print(f"File: {data.get('file_path', 'Unknown')}")
    print(f"Agent Status: {data.get('agent_status', 'Unknown')}")
    
    if data.get('agent_error'):
        print(f"Agent Error: {data['agent_error']}")
    
    if data.get('errors'):
        print(f"Parsing Errors: {len(data['errors'])}")
        for error in data['errors']:
            print(f"  - {error}")
    else:
        print("Parsing Errors: None")
    
    classes = data.get('classes', [])
    print(f"Classes found: {len(classes)}")
    
    for class_info in classes:
        class_name = class_info.get('class_name', 'Unknown')
        methods = class_info.get('methods', [])
        if isinstance(methods, list) and methods:
            method_count = len(methods)
            method_names = methods[:3] if isinstance(methods[0], str) else [m.get('name', 'Unknown') for m in methods[:3]]
            
            print(f"  - {class_name}: {method_count} methods")
            if method_names:
                print(f"    Methods: {', '.join(method_names)}")
                if method_count > 3:
                    print(f"    ... and {method_count - 3} more")
        else:
            print(f"  - {class_name}: No methods")


def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.java_file):
        print(f"Error: File not found: {args.java_file}")
        sys.exit(1)
    
    # Determine output file path
    output_path = create_output_filename(args.java_file, args.output)
    
    try:
        # Run analysis using DuckDuckGo pattern
        output_data = run_with_duckduckgo_pattern(args.java_file, args.verbose)
        
        # Save results
        save_output(output_data, output_path, args.verbose)
        
        # Print summary
        print_summary(output_data)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
