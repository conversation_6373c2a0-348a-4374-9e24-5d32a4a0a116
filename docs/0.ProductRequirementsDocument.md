# Product Requirements Document & Software Requirements Specification
## AI-Powered Java Coding Assistant

**Version:** 0.1
**Date:** [Current Date]
**Status:** Draft
**Author(s):** CodexArchitect, [Your Name/Team Name]

---

## 1. Introduction

### 1.1 Purpose
This document specifies the requirements for the AI-Powered Java Coding Assistant (hereinafter referred to as "the System"). The System is designed to assist Java developers by analyzing Java codebases, generating various forms of documentation and summaries, and providing insights through a Retrieval-Augmented Generation (RAG) powered interface. This document is intended for developers, testers, project managers, and stakeholders involved in the development and evolution of the System.

### 1.2 Scope
The System will provide capabilities to:
*   Parse and analyze Java source code files.
*   Extract structural information (classes, methods, etc.).
*   Trace request flows within Java web applications (from servlet entry points to SQL queries in initial phases).
*   Generate structured JSON summaries of analyzed code.
*   Generate textual descriptions of code structures and flows.
*   (Future) Generate Mermaid sequence diagrams for logic flows.
*   (Future) Generate sections of functional and detail design documents.
*   (Future) Store and retrieve code-related information using a RAG database.
*   (Future) Offer a natural language query interface to interact with the analyzed codebase information.

**Out of Scope for Initial MVP (Phase 0 & 1, unless specified):**
*   Runtime debugging or code execution (beyond analysis tools).
*   Support for languages other than Java.
*   Full IDE integration (initial focus is CLI).
*   Automated code refactoring or generation beyond documentation/summaries.
*   Real-time collaborative features.
*   Advanced dynamic analysis (initial focus is static analysis).

### 1.3 Definitions, Acronyms, and Abbreviations
*   **AI:** Artificial Intelligence
*   **LLM:** Large Language Model
*   **RAG:** Retrieval-Augmented Generation
*   **SRS:** Software Requirements Specification
*   **PRD:** Product Requirements Document
*   **JSP:** JavaServer Pages
*   **SQL:** Structured Query Language
*   **AST:** Abstract Syntax Tree
*   **CLI:** Command-Line Interface
*   **API:** Application Programming Interface
*   **MVP:** Minimum Viable Product
*   **JSON:** JavaScript Object Notation
*   **System:** The AI-Powered Java Coding Assistant

### 1.4 References
*   `phase0.md`: Detailed task list and implementation for Phase 0.
*   (Future links to Phase 1 design documents, RAG architecture, etc.)

### 1.5 Overview
This document is organized as follows:
*   Section 2 provides an overall description of the product, its functions, user characteristics, and constraints.
*   Section 3 details the specific requirements, including functional, non-functional, and interface requirements.
*   Section 4 outlines data requirements.
*   Section 5 summarizes the phased delivery plan.
*   Section 6 lists future considerations.

---

## 2. Overall Description

### 2.1 Product Perspective
The System is a new, standalone software tool. Initially, it will operate as a command-line utility. Future iterations may explore IDE plugin integration or a server-based deployment model for team usage. It leverages external LLM services for natural language processing and generation capabilities.

### 2.2 Product Functions (High-Level Summary)
1.  **Code Analysis:** Statically analyze Java source code to understand its structure and behavior.
2.  **Information Extraction:** Extract key elements like classes, methods, call relationships, and SQL queries.
3.  **Documentation & Summary Generation:** Produce structured data (JSON) and textual summaries based on the analysis.
4.  **RAG Database Management (Future):** Ingest, store, and retrieve analyzed code information efficiently.
5.  **Query Interface:** Allow users to query the System for information about their codebase.

### 2.3 User Characteristics
*   **Java Developers:** Primary users, seeking to understand code, generate documentation, and get quick insights. Assumed to have proficiency in Java and familiarity with CLI tools.
*   **Software Architects/Technical Leads:** Users interested in high-level views, design documentation, and understanding inter-component dependencies.

### 2.4 Operating Environment
*   **Initial (Phase 0, 1):** Local developer machines (Linux, macOS, Windows).
*   **Dependencies:**
    *   Python (3.8+).
    *   Java Development Kit (JDK 8+ for `JavaParser` and running Java-based tools).
    *   Access to an LLM (e.g., via Hugging Face API).
    *   Sufficient RAM and CPU for parsing and LLM interactions.

### 2.5 Design and Implementation Constraints
*   **Technology Stack:**
    *   Primary development language: Python.
    *   AI Agent Framework: `smolagents`.
    *   Java Parsing: `javalang` (Phase 0), `JavaParser` (Phase 1+).
    *   Python-Java Bridge: `Py4J` (Phase 1+).
    *   LLMs: Configurable, initially targeting models like Mistral-7B-Instruct-v0.2.
    *   Vector Database (Future): Open-source options like ChromaDB, Weaviate, or FAISS.
    *   RAG Orchestration (Future): LangChain or LlamaIndex.
*   **Open Source Preference:** Utilize open-source libraries and frameworks where feasible.
*   **Phased Development:** The System will be developed iteratively, as outlined in `phase0.md` and subsequent phase plans.
*   **Initial Interface:** Command-Line Interface (CLI).
*   **Security:** LLM API keys must be handled securely (e.g., via environment variables). The system must not execute arbitrary code derived from user input or analyzed source code beyond the scope of its defined tools.

### 2.6 Assumptions and Dependencies
*   Java source code to be analyzed is syntactically valid for the chosen parser (e.g., `javalang`, `JavaParser`).
*   Users have the necessary runtime environments (Python, Java) installed and configured.
*   Reliable internet connectivity is available for accessing external LLM APIs.
*   The chosen LLM provides sufficiently accurate and relevant responses for the System's tasks.

---

## 3. Specific Requirements

### 3.1 Functional Requirements

#### 3.1.1 Code Analysis (FR-CA)
*   **FR-CA-001:** The System **shall** parse a given Java source file.
    *   **FR-CA-001.1 (Phase 0):** Using `javalang`, extract class names and method names.
*   **FR-CA-002 (Phase 1):** The System **shall** parse multiple Java source files within a specified directory.
*   **FR-CA-003 (Phase 1):** The System **shall**, using `JavaParser`, build an Abstract Syntax Tree (AST) for each Java file.
*   **FR-CA-004 (Phase 1):** The System **shall** trace method calls starting from a user-specified servlet entry point (e.g., `ClassName.methodName`).
    *   **FR-CA-004.1:** Identify internal method calls within the analyzed codebase.
*   **FR-CA-005 (Phase 1):** The System **shall** identify static SQL query strings within Java code, particularly those associated with `PreparedStatement` calls.
*   **FR-CA-006 (Future):** The System **shall** parse JSP files to identify form actions and included files.
*   **FR-CA-007 (Future):** The System **shall** parse `web.xml` or framework-specific annotations to map URLs to servlet classes/methods.

#### 3.1.2 Information & Documentation Generation (FR-DG)
*   **FR-DG-001 (Phase 0):** The System **shall** generate a structured JSON output for a parsed Java file, containing:
    *   `file_path`: The path to the analyzed file.
    *   `classes`: A list of objects, each representing a class, with:
        *   `class_name`: The name of the class.
        *   `methods`: A list of method names within that class.
    *   `errors`: A list of any parsing errors encountered.
*   **FR-DG-002 (Phase 1):** The System **shall** generate a structured JSON output representing a request flow, including:
    *   The entry point method.
    *   A sequence of called methods (call graph snippet).
    *   Identified SQL queries associated with methods in the flow.
*   **FR-DG-003 (Phase 1):** The System **shall** generate a natural language textual description of a traced request flow, based on the output from FR-DG-002, orchestrated by an LLM.
*   **FR-DG-004 (Future):** The System **shall** generate Mermaid syntax for a sequence diagram representing a traced request flow.
*   **FR-DG-005 (Future):** The System **shall** generate textual summaries for individual Java classes or methods using an LLM, augmented by parsed information.
*   **FR-DG-006 (Future):** The System **shall** generate sections of a Functional Design Document (e.g., use case descriptions, screen logic flow) based on analyzed code and user queries.
*   **FR-DG-007 (Future):** The System **shall** generate sections of a Detail Design Document (e.g., method-level logic, data transformations) based on analyzed code and user queries.

#### 3.1.3 Retrieval-Augmented Generation (RAG) (FR-RG)
*   **FR-RG-001 (Future):** The System **shall** process and chunk structured JSON outputs (from FR-DG-001, FR-DG-002) and other relevant data (e.g., Mermaid syntax, source code snippets) for RAG.
*   **FR-RG-002 (Future):** The System **shall** generate embeddings for these chunks using a sentence transformer model.
*   **FR-RG-003 (Future):** The System **shall** store these embeddings and their corresponding text chunks in a vector database.
*   **FR-RG-004 (Future):** The System **shall**, upon receiving a user query, retrieve the top-K most relevant chunks from the vector database.
*   **FR-RG-005 (Future):** The System **shall** augment the prompt to an LLM with the retrieved context and the original user query to generate a final response.

#### 3.1.4 User Interface & Interaction (FR-UI)
*   **FR-UI-001 (Phase 0):** The System **shall** provide a Command-Line Interface (CLI).
    *   **FR-UI-001.1 (Phase 0):** The CLI **shall** accept a Java file path as an argument.
    *   **FR-UI-001.2 (Phase 1):** The CLI **shall** accept a source code directory path and an entry point (e.g., `ClassName.methodName`) as arguments for flow analysis.
*   **FR-UI-002 (Phase 0):** The System **shall** output results (JSON, textual descriptions) to the console and/or a specified output file.
*   **FR-UI-003 (Future):** The System **shall** allow users to ask natural language questions about the codebase via the CLI, which are then processed by the RAG-enabled LLM.

### 3.2 Non-Functional Requirements

#### 3.2.1 Performance (NFR-PE)
*   **NFR-PE-001:** Parsing of a single, average-sized Java file (e.g., 500-1000 LOC) **should** complete within TBD seconds on a standard developer machine (e.g., Core i5/i7, 8GB RAM).
*   **NFR-PE-002:** Request flow analysis for a simple flow (e.g., 5-10 method calls) **should** complete within TBD seconds.
*   **NFR-PE-003 (Future):** RAG query response time (excluding LLM generation time) **should** be within TBD milliseconds for typical queries.

#### 3.2.2 Usability (NFR-US)
*   **NFR-US-001:** CLI commands **shall** be intuitive and well-documented (e.g., via `--help`).
*   **NFR-US-002:** Error messages **shall** be clear, informative, and guide the user towards resolving issues.
*   **NFR-US-003:** Output formats (JSON, text) **shall** be human-readable and machine-parseable where appropriate.

#### 3.2.3 Reliability & Robustness (NFR-RL)
*   **NFR-RL-001:** The System **shall** handle common parsing errors (e.g., syntax errors in Java code) gracefully, reporting them without crashing.
*   **NFR-RL-002:** The System **shall** manage dependencies on external services (like LLM APIs) with appropriate error handling and retries (where applicable).

#### 3.2.4 Maintainability & Extensibility (NFR-MA)
*   **NFR-MA-001:** The codebase **shall** be modular, with clear separation of concerns (e.g., parsing tools, agent logic, RAG components).
*   **NFR-MA-002:** The System **shall** be designed to allow for the addition of new analysis capabilities or output formats with reasonable effort.
*   **NFR-MA-003:** Code **shall** be well-commented and adhere to consistent coding standards.

#### 3.2.5 Security (NFR-SE)
*   **NFR-SE-001:** Sensitive information, such as LLM API keys, **shall not** be hardcoded and **shall** be managed via environment variables or secure configuration methods.
*   **NFR-SE-002:** The System **shall not** execute arbitrary code from the analyzed Java source files. Its interaction with Java code is limited to parsing and static analysis via trusted libraries.

#### 3.2.6 Portability (NFR-PO)
*   **NFR-PO-001:** The Python components of the System **shall** be executable on common operating systems (Windows, macOS, Linux) where Python is supported.
*   **NFR-PO-002:** Java-based tools used by the System (e.g., `JavaParser` via `Py4J`) **shall** be compatible with standard JDK versions.

### 3.3 Interface Requirements

#### 3.3.1 User Interfaces
*   **IF-UI-001:** Command-Line Interface (CLI) as described in FR-UI.

#### 3.3.2 Software Interfaces
*   **IF-SW-001 (Input):** Java source files (`.java`).
*   **IF-SW-002 (Input, Future):** Configuration files (e.g., for RAG parameters, LLM model selection).
*   **IF-SW-003 (Output):** JSON files, plain text files, console output.
*   **IF-SW-004 (Output, Future):** Mermaid syntax files (`.mmd` or embedded in other docs).
*   **IF-SW-005 (External API):** Hugging Face Inference API (or other configured LLM provider API) using HTTPS.

#### 3.3.3 Hardware Interfaces
*   None specific beyond standard computer hardware capable of running Python, Java, and accessing the internet.

#### 3.3.4 Communications Interfaces
*   **IF-COM-001:** HTTPS for communication with external LLM APIs.

---

## 4. Data Requirements

### 4.1 Data Description
*   **Input Data:** Java source code text.
*   **Intermediate Data:**
    *   ASTs representing Java code structure.
    *   Call graph information (caller-callee relationships).
    *   Extracted SQL query strings.
    *   Structured summaries of classes and methods.
*   **Output Data:**
    *   JSON files containing structured analysis results.
    *   Textual descriptions and summaries.
    *   (Future) Mermaid diagram syntax.
*   **RAG Data (Future):**
    *   Text chunks derived from source code, summaries, and generated documentation.
    *   Vector embeddings of these text chunks.
    *   Metadata associated with each chunk (e.g., source file, class/method name, data type).

### 4.2 Data Formats
*   **Primary Intermediate & Output Format:** JSON.
*   **Textual Output:** Plain text (UTF-8).
*   **Diagrams (Future):** Mermaid syntax (plain text).

### 4.3 Data Storage (RAG - Future)
*   A vector database (e.g., ChromaDB, Weaviate, FAISS index files) will be used to store embeddings and associated text chunks.
*   Configuration for the vector database connection and schema will be managed externally.

---

## 5. Phased Delivery Plan (Summary)

*   **Phase 0 (Completed):** Basic Java file parsing (`javalang`), `smolagents` setup, structured JSON output for classes/methods. (Reference: `phase0.md`)
*   **Phase 1 (Current/Next):** Request flow analysis from servlet entry points to SQL queries (`JavaParser`, `Py4J`), enhanced JSON output, textual flow descriptions.
*   **Phase 2 (Future):** Core RAG implementation (vector DB, embedding, retrieval), initial natural language querying.
*   **Phase 3 (Future):** Mermaid diagram generation, advanced documentation generation, JSP parsing basics, refinement of RAG capabilities.
*   **Subsequent Phases:** Full IDE integration, support for ORMs, dynamic SQL heuristics, etc.

---

## 6. Future Considerations / Out of Scope for Initial Releases

*   Full, interactive IDE integration.
*   Support for other programming languages.
*   Advanced dynamic code analysis or runtime profiling.
*   Automated code generation for functional logic (beyond documentation/summaries).
*   Sophisticated UI beyond CLI (e.g., web interface).
*   User authentication and multi-user support for server-based deployments.
*   Analysis of compiled bytecode (`.class` files) instead of source code.

---