# Phase 0: Basic Java Parsing & Structured Output with `smolagents`

**Key Goal for Phase 0:**
To create a command-line AI assistant using `smolagents` that takes a Java file path as input, uses `javalang` to parse it, extracts class and method information, and outputs this information as a well-structured JSON file. This JSON output will be the primary artifact for future RAG ingestion, and the AI agent's role is to orchestrate this process and present the structured data.

**Estimated Time:** 1-2 days (depending on familiarity with tools)

**Tools:**
*   Python 3.8+
*   `smolagents`
*   `javalang`
*   An LLM accessible via `smolagents` (e.g., Mistral-7B-Instruct-v0.2 via Hugging Face API)

---

## Step-by-Step Task Details:

### Task 1: Project Setup & Environment Configuration

*   **1.1. Create Project Directory:**
    *   **Action:** Create a new directory for your project (e.g., `java_ai_assistant`).
    *   **Detail:**
        ```bash
        mkdir java_ai_assistant && cd java_ai_assistant
        ```
*   **1.2. Set Up Python Virtual Environment:**
    *   **Action:** Create and activate a Python virtual environment.
    *   **Detail:**
        ```bash
        python -m venv venv
        # On Linux/macOS:
        source venv/bin/activate
        # On Windows:
        # venv\Scripts\activate
        ```
*   **1.3. Install Dependencies:**
    *   **Action:** Install `smolagents` and `javalang`.
    *   **Detail:**
        ```bash
        pip install smolagents javalang
        ```
*   **1.4. Configure Hugging Face API Access (if using HF models):**
    *   **Action:** Ensure your Hugging Face API token is available as an environment variable.
    *   **Detail:**
        ```bash
        export HF_TOKEN="your_hf_api_token_here"
        ```
        (Add this to your `~/.bashrc`, `~/.zshrc`, or set it for your current session. For Windows, use `set HF_TOKEN="your_hf_api_token_here"` in Command Prompt or `$env:HF_TOKEN="your_hf_api_token_here"` in PowerShell).
*   **1.5. Initialize Git Repository:**
    *   **Action:** Initialize a Git repository for version control.
    *   **Detail:**
        ```bash
        git init
        echo "venv/" > .gitignore
        echo "*.pyc" >> .gitignore
        echo "__pycache__/" >> .gitignore
        # Add other project-specific ignores if necessary (e.g., IDE files, output files)
        echo "summary_output.json" >> .gitignore 
        git add .gitignore
        git commit -m "Initial project setup and .gitignore"
        ```

### Task 2: Develop the Java Parsing Tool (`javalang`)

*   **2.1. Create `java_parser_tool.py`:**
    *   **Action:** Create a new Python file (e.g., `java_parser_tool.py`) in your project root to house your parsing logic.
*   **2.2. Implement `parse_java_file` Function:**
    *   **Action:** Define a Python function that takes a `file_path` (string) as input.
    *   **Detail:**
        *   Use `javalang.parse.parse()` to parse the Java file content.
        *   Iterate through the Abstract Syntax Tree (AST) to find `javalang.tree.ClassDeclaration` and `javalang.tree.MethodDeclaration` nodes.
        *   Extract class names and method names.
        *   Structure the output as a dictionary that is RAG-friendly. Include metadata like the file path.
        *   Example implementation:
            ```python
            # Inside java_parser_tool.py
            import javalang
            from smolagents import tool # Import the tool decorator

            @tool # Decorate the function so smolagents can use it
            def parse_java_file(file_path: str) -> dict:
                """
                Parses a Java file and extracts structured information about its classes and methods.
                Returns a dictionary containing the file path and a list of class details.
                """
                parsed_data = {"file_path": file_path, "classes": [], "errors": []}
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read()
                    tree = javalang.parse.parse(content)
                    
                    for _, node in tree.filter(javalang.tree.ClassDeclaration): # Use filter for specific node types
                        class_info = {
                            "class_name": node.name,
                            "methods": [],
                            # Future enhancements: 
                            # "start_line": node.position.line if node.position else None,
                            # "end_line": node.end_position.line if hasattr(node, 'end_position') and node.end_position else None,
                            # "package": tree.package.name if tree.package else None,
                            # "imports": [imp.path for imp in tree.imports]
                        }
                        for method_node in node.methods:
                            class_info["methods"].append(method_node.name)
                        parsed_data["classes"].append(class_info)
                        
                except FileNotFoundError:
                    parsed_data["errors"].append(f"File not found: {file_path}")
                except javalang.parser.JavaSyntaxError as e:
                    # Ensure e.at is not None before accessing line/column
                    location = "unknown location"
                    if e.at:
                        location = f"line {e.at.line}, column {e.at.column}"
                    parsed_data["errors"].append(f"Java syntax error in {file_path}: {e.description} at {location}")
                except Exception as e:
                    parsed_data["errors"].append(f"An unexpected error occurred while parsing {file_path}: {str(e)}")
                
                return parsed_data
            ```
*   **2.3. Add Error Handling:**
    *   **Action:** Implement robust error handling within `parse_java_file`.
    *   **Detail:** Catch `FileNotFoundError`, `javalang.parser.JavaSyntaxError`, and general exceptions. Include error messages in the returned dictionary (as shown in the example above).
*   **2.4. Decorate with `@tool`:**
    *   **Action:** Import `tool` from `smolagents` and decorate your `parse_java_file` function.
    *   **Detail:** `from smolagents import tool` and `@tool` above the function definition.

### Task 3: Implement the `smolagents` AI Agent

*   **3.1. Create `main.py` (or `agent_runner.py`):**
    *   **Action:** Create the main script in your project root that will initialize and run the `smolagents` agent.
*   **3.2. Import Necessary Modules:**
    *   **Action:** Import `CodeAgent`, `HfApiModel` from `smolagents`, your `parse_java_file` tool, `sys` for arguments, and `json` for output.
    *   **Detail:**
        ```python
        # Inside main.py
        import sys
        import json
        import os # For creating output directory
        from smolagents import CodeAgent, HfApiModel
        from java_parser_tool import parse_java_file # Import your tool
        ```
*   **3.3. Initialize the Language Model:**
    *   **Action:** Create an instance of `HfApiModel`.
    *   **Detail (within an `if __name__ == "__main__":` block in `main.py`):**
        ```python
        model = HfApiModel("mistralai/Mistral-7B-Instruct-v0.2") # Or your preferred model
        # Ensure HF_TOKEN is set in your environment
        ```
*   **3.4. Initialize the `CodeAgent`:**
    *   **Action:** Create an instance of `CodeAgent`, passing the LLM model and a list containing your `parse_java_file` tool.
    *   **Detail:**
        ```python
        agent = CodeAgent(model=model, tools=[parse_java_file])
        ```
*   **3.5. Define the Agent's Task/Prompt:**
    *   **Action:** Craft a prompt that instructs the agent to use the parsing tool and return the structured information.
    *   **Detail:** The agent's primary role here is to invoke the tool correctly. The LLM helps interpret the natural language request into a tool call.
        ```python
        # java_file_to_analyze will be defined from sys.argv later
        # prompt = f"""
        # Please analyze the Java file located at '{java_file_to_analyze}'. 
        # Use the 'parse_java_file' tool to extract information about its classes and methods. 
        # Your response should primarily be the structured JSON output from the tool.
        # """
        # A more direct prompt for smolagents to just execute the tool and return its output:
        prompt = f"Execute the 'parse_java_file' tool with the file_path '{java_file_to_analyze}'. Return the direct output of this tool."
        ```

### Task 4: Main Execution Script Logic

*   **4.1. Handle Command-Line Arguments:**
    *   **Action:** Use `sys.argv` (or `argparse` for more robustness) to get the Java file path from the command line.
    *   **Detail (within `if __name__ == "__main__":` block in `main.py`):**
        ```python
        if len(sys.argv) < 2:
            print("Usage: python main.py <path_to_java_file>")
            sys.exit(1)
        java_file_to_analyze = sys.argv[1]

        # Update prompt with the actual file path
        prompt = f"Execute the 'parse_java_file' tool with the file_path '{java_file_to_analyze}'. Return the direct output of this tool."
        ```
*   **4.2. Run the Agent:**
    *   **Action:** Call the `agent.run()` method with the defined prompt.
    *   **Detail:**
        ```python
        print(f"Analyzing {java_file_to_analyze}...")
        agent_response_str = agent.run(prompt) 
        ```
*   **4.3. Process and Save Agent's Response:**
    *   **Action:** The `agent.run()` method returns a string. If the prompt is well-crafted and the LLM understands to return the tool's output, this string *should be* the JSON representation of the dictionary returned by `parse_java_file`.
    *   **Detail:**
        ```python
        print("\nAgent Response (raw string):")
        print(agent_response_str)

        output_data = None
        try:
            # The agent's response should ideally be the direct JSON string from the tool
            output_data = json.loads(agent_response_str)
        except json.JSONDecodeError:
            print(f"Warning: Could not decode JSON directly from agent response: '{agent_response_str}'")
            print("This might happen if the LLM added conversational text.")
            print("Attempting to re-run the tool directly for saving its output.")
            # Fallback: directly call the tool to ensure we get its structured output
            # This bypasses the LLM's interpretation for the final saved JSON,
            # ensuring data integrity for RAG.
            try:
                output_data = parse_java_file(java_file_to_analyze)
                print("Successfully retrieved data by calling the tool directly.")
            except Exception as e:
                print(f"Error calling tool directly: {e}")
                output_data = {
                    "error": "Failed to get structured data from tool or agent",
                    "raw_agent_response": agent_response_str,
                    "tool_input_file": java_file_to_analyze
                }
        
        # Create an output directory if it doesn't exist
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate a unique filename based on the input file
        base_filename = os.path.basename(java_file_to_analyze)
        output_filename = os.path.join(output_dir, f"{os.path.splitext(base_filename)[0]}_summary.json")

        with open(output_filename, "w", encoding='utf-8') as f:
            json.dump(output_data, f, indent=4)
        print(f"\nStructured output saved to {output_filename}")
        ```
*   **4.4. Print Confirmation:**
    *   **Action:** Print the agent's response (raw) and a confirmation message about the saved file.

### Task 5: Testing

*   **5.1. Create Sample Java Files:**
    *   **Action:** Create a directory `test_java_files` and add simple Java files for testing.
    *   **Detail:**
        *   `test_java_files/Test1.java`:
            ```java
            package com.example;
            public class Test1 { 
                public void methodA() {} 
                private int fieldX;
            }
            ```
        *   `test_java_files/Test2.java`:
            ```java
            public class Test2 { 
                public void methodX() {} 
                public void methodY(String s) {} 
            } 
            class AnotherClassInSameFile { 
                void packageMethod() {} 
            }
            ```
        *   `test_java_files/Empty.java`:
            ```java
            public class Empty {}
            ```
        *   `test_java_files/Broken.java`:
            ```java
            public class Broken { 
                public void oops { // Syntax error: missing parentheses
            }
            ```
*   **5.2. Run `main.py` with Test Files:**
    *   **Action:** Execute your script with each test file.
    *   **Detail:**
        ```bash
        python main.py test_java_files/Test1.java
        python main.py test_java_files/Test2.java
        python main.py test_java_files/Empty.java
        python main.py test_java_files/Broken.java
        ```
*   **5.3. Verify Output:**
    *   **Action:** Check the console output and the content of the generated JSON files in the `output/` directory.
    *   **Detail:** Ensure class/method names are correct, file path is included, and errors (like syntax errors in `Broken.java`) are reported gracefully in the JSON.

### Task 6: Documentation & Version Control

*   **6.1. Create `README.md`:**
    *   **Action:** Write a `README.md` file in the project root.
    *   **Detail:** Include:
        *   Project purpose (Phase 0).
        *   Setup instructions (virtual env, `pip install -r requirements.txt` (see below), HF_TOKEN).
        *   Usage instructions (`python main.py <java_file_path>`).
        *   Example of the output JSON structure.
*   **6.2. Create `requirements.txt`:**
    *   **Action:** Generate a `requirements.txt` file.
    *   **Detail:**
        ```bash
        pip freeze > requirements.txt
        ```
*   **6.3. Add Code Comments:**
    *   **Action:** Ensure your Python code (`java_parser_tool.py`, `main.py`) is well-commented with docstrings and inline comments where necessary.
*   **6.4. Commit Changes to Git:**
    *   **Action:** Add and commit your work.
    *   **Detail:**
        ```bash
        git add java_parser_tool.py main.py README.md requirements.txt test_java_files/ output/ # Add output to .gitignore if not versioning example outputs
        git commit -m "Implemented Phase 0: Basic Java parsing and structured JSON output via smolagents"
        ```

### Task 7: Review and Prepare for Next Phase

*   **7.1. Assess Phase 0 Goals:**
    *   **Action:** Confirm that the assistant correctly parses Java files and produces the desired structured JSON output.
*   **7.2. Evaluate JSON for RAG:**
    *   **Action:** Review the output JSON structure. Is it granular enough? Does it contain the necessary metadata for effective retrieval in a RAG system? (For Phase 0, `file_path`, `class_name`, `method_names` is a good start. Consider adding line numbers or package info if easily obtainable and useful).
*   **7.3. Identify Lessons Learned:**
    *   **Action:** Note any challenges faced (e.g., LLM misinterpreting prompts, `javalang` limitations), potential improvements to the parsing, or insights into `smolagents` behavior. This will inform subsequent phases.

---