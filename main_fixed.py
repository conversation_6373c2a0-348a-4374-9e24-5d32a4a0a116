#!/usr/bin/env python3
"""
Fixed version based on the article pattern and your observation
The agent is working but file path gets corrupted - let's fix this
"""

import sys
import json
import os
import argparse
from typing import Dict, Any

# Import our Java parser tool
from java_parser_tool import parse_java_file


def setup_argument_parser():
    """Set up command-line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Fixed Java Parsing with CodeAgent",
        epilog="Fixes the file path corruption issue"
    )
    
    parser.add_argument("java_file", help="Path to the Java file to analyze")
    parser.add_argument("--output", "-o", help="Output file path")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--fallback", action="store_true", help="Use fallback mode")
    
    return parser


def create_output_filename(java_file_path: str, custom_output: str = None) -> str:
    """Create an appropriate output filename."""
    if custom_output:
        return custom_output
        
    os.makedirs("output", exist_ok=True)
    base_filename = os.path.basename(java_file_path)
    name_without_ext = os.path.splitext(base_filename)[0]
    return os.path.join("output", f"{name_without_ext}_fixed.json")


def run_with_fixed_agent(java_file_path: str, verbose: bool = False) -> Dict[str, Any]:
    """Run with fixed agent that handles file path properly"""
    
    if verbose:
        print("🔧 Running with Fixed CodeAgent")
        print(f"   Input file: '{java_file_path}'")
    
    try:
        if verbose:
            print("Step 1: Importing smolagents...")
        
        from smolagents import CodeAgent, HfApiModel
        
        if verbose:
            print("✅ Imports successful")
            print("Step 2: Creating agent...")
        
        # Create agent using the pattern
        agent = CodeAgent(tools=[parse_java_file], model=HfApiModel())
        
        if verbose:
            print("✅ Agent created successfully")
            print("Step 3: Preparing prompt with exact file path...")
        
        # Create a more precise prompt that preserves the file path
        prompt = f"""Execute the parse_java_file function with the exact file path: {java_file_path}

Important: Use the exact path without any modifications: {java_file_path}"""
        
        if verbose:
            print(f"   Prompt: {prompt}")
            print("   Calling agent.run()...")
        
        # Run the agent
        response = agent.run(prompt)
        
        if verbose:
            print("✅ Agent execution completed")
            print(f"   Response type: {type(response)}")
            if response:
                response_str = str(response)
                print(f"   Response preview: {response_str[:200]}...")
        
        # Parse the response
        if response is None:
            if verbose:
                print("⚠️  Agent returned None, using direct tool call")
            return parse_java_file(java_file_path)
        
        # Handle the "Final answer:" format from the agent
        response_str = str(response)
        
        # Check if response contains "Final answer:"
        if "Final answer:" in response_str:
            if verbose:
                print("📋 Found 'Final answer:' in response")
            
            # Extract the part after "Final answer:"
            final_answer_part = response_str.split("Final answer:")[-1].strip()
            
            if verbose:
                print(f"   Final answer part: {final_answer_part[:100]}...")
            
            try:
                # Try to parse the final answer as JSON
                result = json.loads(final_answer_part)
                
                if verbose:
                    print("✅ Successfully parsed final answer as JSON")
                
                # Check if the file path got corrupted
                if result.get('file_path') != java_file_path:
                    if verbose:
                        print(f"⚠️  File path corrupted: '{result.get('file_path')}' != '{java_file_path}'")
                        print("   Fixing file path and re-running tool...")
                    
                    # File path was corrupted, run the tool directly with correct path
                    corrected_result = parse_java_file(java_file_path)
                    corrected_result["agent_status"] = "path_corrected"
                    corrected_result["original_corrupted_path"] = result.get('file_path')
                    return corrected_result
                else:
                    if verbose:
                        print("✅ File path is correct")
                    result["agent_status"] = "success"
                    return result
                    
            except json.JSONDecodeError as e:
                if verbose:
                    print(f"❌ Failed to parse final answer as JSON: {e}")
                    print("   Using direct tool call...")
                
                result = parse_java_file(java_file_path)
                result["agent_status"] = "json_parse_failed"
                result["agent_response"] = final_answer_part[:200]
                return result
        
        # If no "Final answer:" found, try to parse the whole response
        try:
            result = json.loads(response_str)
            if verbose:
                print("✅ Parsed full response as JSON")
            result["agent_status"] = "direct_json"
            return result
        except json.JSONDecodeError:
            if verbose:
                print("⚠️  Could not parse response as JSON, using direct tool call")
            
            result = parse_java_file(java_file_path)
            result["agent_status"] = "fallback"
            result["agent_response"] = response_str[:200] + "..." if len(response_str) > 200 else response_str
            return result
            
    except Exception as e:
        if verbose:
            print(f"❌ Agent failed: {e}")
            print("   Using direct tool call...")
        
        result = parse_java_file(java_file_path)
        result["agent_status"] = "error"
        result["agent_error"] = str(e)
        return result


def save_output(data: Dict[str, Any], output_path: str, verbose: bool = False) -> None:
    """Save the analysis results to a JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        print(f"Analysis complete. Results saved to: {output_path}")
            
    except Exception as e:
        print(f"Error saving output to {output_path}: {e}")


def print_summary(data: Dict[str, Any]) -> None:
    """Print a brief summary of the analysis results."""
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY (FIXED VERSION)")
    print("="*50)
    
    print(f"File: {data.get('file_path', 'Unknown')}")
    print(f"Agent Status: {data.get('agent_status', 'Unknown')}")
    
    if data.get('original_corrupted_path'):
        print(f"Original Corrupted Path: {data['original_corrupted_path']}")
        print("✅ Path was automatically corrected")
    
    if data.get('agent_error'):
        print(f"Agent Error: {data['agent_error']}")
    
    if data.get('errors'):
        print(f"Parsing Errors: {len(data['errors'])}")
        for error in data['errors']:
            print(f"  - {error}")
    else:
        print("Parsing Errors: None")
    
    classes = data.get('classes', [])
    print(f"Classes found: {len(classes)}")
    
    for class_info in classes:
        class_name = class_info.get('class_name', 'Unknown')
        methods = class_info.get('methods', [])
        if isinstance(methods, list) and methods:
            method_count = len(methods)
            method_names = methods[:3] if isinstance(methods[0], str) else [m.get('name', 'Unknown') for m in methods[:3]]
            
            print(f"  - {class_name}: {method_count} methods")
            if method_names:
                print(f"    Methods: {', '.join(method_names)}")
                if method_count > 3:
                    print(f"    ... and {method_count - 3} more")
        else:
            print(f"  - {class_name}: No methods")


def main():
    """Main execution function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.java_file):
        print(f"Error: File not found: {args.java_file}")
        sys.exit(1)
    
    if not args.java_file.endswith('.java'):
        print(f"Warning: File does not have .java extension: {args.java_file}")
    
    # Determine output file path
    output_path = create_output_filename(args.java_file, args.output)
    
    try:
        # Run analysis
        if args.fallback:
            if args.verbose:
                print("Using fallback mode (direct tool call)")
            output_data = parse_java_file(args.java_file)
            output_data["agent_status"] = "fallback_mode"
        else:
            output_data = run_with_fixed_agent(args.java_file, args.verbose)
        
        # Save results
        save_output(output_data, output_path, args.verbose)
        
        # Print summary
        print_summary(output_data)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
