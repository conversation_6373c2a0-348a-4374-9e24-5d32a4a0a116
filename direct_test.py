#!/usr/bin/env python3
"""
Direct test of your exact smolagents code
"""

print("Starting direct test...")

try:
    print("Step 1: Importing smolagents...")
    from smolagents import CodeAgent, DuckDuckGoSearchTool, HfApiModel
    print("✅ Imports successful")
    
    print("Step 2: Creating agent...")
    agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=HfApiModel())
    print("✅ Agent created")
    
    print("Step 3: Running agent...")
    response = agent.run("How many seconds would it take for a leopard at full speed to run through Pont des Arts?")
    print("✅ Agent completed")
    
    print("Response:")
    print(response)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
